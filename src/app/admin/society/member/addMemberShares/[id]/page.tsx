'use client'

import React, { useState, useEffect } from 'react'

import { useRouter, useParams } from 'next/navigation'

import {
  Box,
  Button,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  Divider,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  FormControlLabel,
  Radio,
  RadioGroup,
  FormControl
} from '@mui/material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import AddIcon from '@mui/icons-material/Add'
import DeleteIcon from '@mui/icons-material/Delete'

import axios from 'axios'

interface Nominee {
  nominee_name: string
  nominee_address: string
  percentage: string
  guardian_name?: string
  guardian_address?: string
  relation_with_minor?: string
  is_minor: boolean
}

interface ShareCertificateFormData {
  certificate_no: string
  member_reg_no: string
  admission_date: string
  entrance_fee: string
  full_name: string
  address: string
  occupation: string
  age_on_admission: string
  cessation_date: string
  cessation_reason: string
  no_of_shares: string
  share_series_start: string
  share_series_end: string
  share_value: string
  total_amount: string
  nominees: Nominee[]
}

const AddEditShareCertificate: React.FC = () => {
  const router = useRouter()
  const params = useParams()
  const memberId = params.id as string

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const [formData, setFormData] = useState<ShareCertificateFormData>({
    certificate_no: '',
    member_reg_no: '',
    admission_date: '',
    entrance_fee: '',
    full_name: '',
    address: '',
    occupation: '',
    age_on_admission: '',
    cessation_date: '',
    cessation_reason: '',
    no_of_shares: '',
    share_series_start: '',
    share_series_end: '',
    share_value: '',
    total_amount: '',
    nominees: []
  })

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)

        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}/admin/member/viewMemberShares/${memberId}`,
          { withCredentials: true }
        )

        if (response.data.status === 'success' && response.data.data) {
          const data = response.data.data

          setFormData({
            certificate_no: data.certificate_no || '',
            member_reg_no: data.member_reg_no || '',
            admission_date: data.admission_date ? data.admission_date.split('T')[0] : '',
            entrance_fee: data.entrance_fee?.toString() || '',
            full_name: data.full_name || '',
            address: data.address || '',
            occupation: data.occupation || '',
            age_on_admission: data.age_on_admission?.toString() || '',
            cessation_date: data.cessation_date ? data.cessation_date.split('T')[0] : '',
            cessation_reason: data.ceasing_reason || '',
            no_of_shares: data.no_of_shares?.toString() || '',
            share_series_start: data.share_series_start?.toString() || '',
            share_series_end: data.share_series_end?.toString() || '',
            share_value: data.share_value?.toString() || '',
            total_amount: data.amount_paid?.toString() || '',
            nominees: data.nominees?.map((nominee: any) => ({
              nominee_name: nominee.nominee_name || '',
              nominee_address: nominee.nominee_address || '',
              percentage: nominee.percentage || '',
              guardian_name: nominee.guardian_name || '',
              guardian_address: nominee.guardian_address || '',
              relation_with_minor: nominee.relation_with_minor || '',
              is_minor: nominee.is_minor || false
            })) || []
          })
        }
      } catch (error) {
        console.error('Error fetching data:', error)
      } finally {
        setLoading(false)
      }
    }

    if (memberId) {
      fetchData()
    }
  }, [memberId])

  const handleInputChange = (field: keyof ShareCertificateFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleNomineeChange = (index: number, field: keyof Nominee, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      nominees: prev.nominees.map((nominee, i) => 
        i === index ? { ...nominee, [field]: value } : nominee
      )
    }))
  }

  const addNominee = () => {
    setFormData(prev => ({
      ...prev,
      nominees: [...prev.nominees, {
        nominee_name: '',
        nominee_address: '',
        percentage: '',
        guardian_name: '',
        guardian_address: '',
        relation_with_minor: '',
        is_minor: false
      }]
    }))
  }

  const removeNominee = (index: number) => {
    setFormData(prev => ({
      ...prev,
      nominees: prev.nominees.filter((_, i) => i !== index)
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)

      // Add your save API call here
      console.log('Saving data:', formData)

      // await axios.post/put API call
      router.back()
    } catch (error) {
      console.error('Error saving data:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleBack = () => {
    router.back()
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    )
  }

  return (
    <Box sx={{ p: 3, maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant='h4' component='h1' sx={{ fontWeight: 'bold' }}>
          New / Edit Share Certificates
        </Typography>
        <Button 
          variant='contained' 
          startIcon={<ArrowBackIcon />} 
          onClick={handleBack} 
          sx={{ bgcolor: '#666' }}
        >
          Back
        </Button>
      </Box>

      <Card sx={{ boxShadow: 3 }}>
        <CardContent sx={{ p: 4 }}>
          {/* Share Certificate Details Section */}
          <Typography variant='h5' component='h2' sx={{ mb: 3, fontWeight: 'bold' }}>
            Share Certificate Details
          </Typography>

          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Certificate No*"
                value={formData.certificate_no}
                onChange={(e) => handleInputChange('certificate_no', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Member Registration No*"
                value={formData.member_reg_no}
                onChange={(e) => handleInputChange('member_reg_no', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Admission Date"
                type="date"
                value={formData.admission_date}
                onChange={(e) => handleInputChange('admission_date', e.target.value)}
                variant="outlined"
                slotProps={{
                  inputLabel: { shrink: true }
                }}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Entrance Fee"
                value={formData.entrance_fee}
                onChange={(e) => handleInputChange('entrance_fee', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Full Name"
                value={formData.full_name}
                onChange={(e) => handleInputChange('full_name', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Occupation"
                value={formData.occupation}
                onChange={(e) => handleInputChange('occupation', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Age On Admission"
                value={formData.age_on_admission}
                onChange={(e) => handleInputChange('age_on_admission', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Date of Cessation"
                type="date"
                value={formData.cessation_date}
                onChange={(e) => handleInputChange('cessation_date', e.target.value)}
                variant="outlined"
                slotProps={{
                  inputLabel: { shrink: true }
                }}
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Cessation Reason"
                value={formData.cessation_reason}
                onChange={(e) => handleInputChange('cessation_reason', e.target.value)}
                variant="outlined"
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 4 }} />

          {/* Share Details Section */}
          <Typography variant='h5' component='h2' sx={{ mb: 3, fontWeight: 'bold' }}>
            Share Details
          </Typography>

          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Number Of Shares*"
                value={formData.no_of_shares}
                onChange={(e) => handleInputChange('no_of_shares', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Share Serial Numbers*"
                placeholder="e.g., 1001-1010"
                value={`${formData.share_series_start}-${formData.share_series_end}`}
                onChange={(e) => {
                  const [start, end] = e.target.value.split('-')

                  handleInputChange('share_series_start', start || '')
                  handleInputChange('share_series_end', end || '')
                }}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Share Value*"
                value={formData.share_value}
                onChange={(e) => handleInputChange('share_value', e.target.value)}
                variant="outlined"
              />
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <TextField
                fullWidth
                label="Total Amount*"
                value={formData.total_amount}
                onChange={(e) => handleInputChange('total_amount', e.target.value)}
                variant="outlined"
              />
            </Grid>
          </Grid>

          <Divider sx={{ my: 4 }} />

          {/* Nominee Details Section */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant='h5' component='h2' sx={{ fontWeight: 'bold' }}>
              Nominee Details
            </Typography>
            <Button
              variant='contained'
              startIcon={<AddIcon />}
              onClick={addNominee}
              sx={{ bgcolor: '#1976d2' }}
            >
              Add Nominee
            </Button>
          </Box>

          {formData.nominees.length > 0 && (
            <TableContainer component={Paper} sx={{ boxShadow: 1 }}>
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: '#f5f5f5' }}>
                    <TableCell sx={{ fontWeight: 'bold' }}>Nominee Name</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Percentage</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Address</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Is Minor</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Guardian Name</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Guardian Address</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Relation with Minor</TableCell>
                    <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {formData.nominees.map((nominee, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <TextField
                          size="small"
                          value={nominee.nominee_name}
                          onChange={(e) => handleNomineeChange(index, 'nominee_name', e.target.value)}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          value={nominee.percentage}
                          onChange={(e) => handleNomineeChange(index, 'percentage', e.target.value)}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          value={nominee.nominee_address}
                          onChange={(e) => handleNomineeChange(index, 'nominee_address', e.target.value)}
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <FormControl>
                          <RadioGroup
                            row
                            value={nominee.is_minor ? 'yes' : 'no'}
                            onChange={(e) => handleNomineeChange(index, 'is_minor', e.target.value === 'yes')}
                          >
                            <FormControlLabel value="yes" control={<Radio size="small" />} label="Yes" />
                            <FormControlLabel value="no" control={<Radio size="small" />} label="No" />
                          </RadioGroup>
                        </FormControl>
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          value={nominee.guardian_name}
                          onChange={(e) => handleNomineeChange(index, 'guardian_name', e.target.value)}
                          variant="outlined"
                          disabled={!nominee.is_minor}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          value={nominee.guardian_address}
                          onChange={(e) => handleNomineeChange(index, 'guardian_address', e.target.value)}
                          variant="outlined"
                          disabled={!nominee.is_minor}
                        />
                      </TableCell>
                      <TableCell>
                        <TextField
                          size="small"
                          value={nominee.relation_with_minor}
                          onChange={(e) => handleNomineeChange(index, 'relation_with_minor', e.target.value)}
                          variant="outlined"
                          disabled={!nominee.is_minor}
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          color="error"
                          onClick={() => removeNominee(index)}
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}

          {/* Save Button */}
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant='contained'
              onClick={handleSave}
              disabled={saving}
              sx={{ bgcolor: '#1976d2', minWidth: 120 }}
            >
              {saving ? <CircularProgress size={24} /> : 'Save'}
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  )
}

export default AddEditShareCertificate
