"use client";

import React, { useState, useEffect } from "react";

import { useRouter } from "next/navigation";

import dayjs from "dayjs";
import axios from "axios";
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  FormControl,
  IconButton,
  MenuItem,
  Paper,
  Select,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Alert,
  Tooltip,
  Typography
} from "@mui/material";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { toast } from 'react-toastify';

interface AllotteeRow {
  id: number;
  building: string;
  floor: string;
  unitNumber: string;
  unitCategory: string;
  name: string;
  mobileNo: string;
  email: string;
  allotteeType: string;
  effectiveDate: Date | null;
}

const NewBulkUnit: React.FC = () => {
  const router = useRouter();
  
  // Ref to track if we've already fetched data to prevent duplicate calls
  const hasCalledAPI = React.useRef(false);

  // Back button handler
  const onBackClick = () => {
    router.back();
  };

  // Helper function to create an empty row with an ID
  const createEmptyRow = (id: number): AllotteeRow => ({
    id,
    building: "",
    floor: "",
    unitNumber: "",
    unitCategory: "",
    name: "",
    mobileNo: "",
    email: "",
    allotteeType: "",
    effectiveDate: null,
  });

  // Initialize with 10 empty rows
  const [rows, setRows] = useState<AllotteeRow[]>(
    Array.from({ length: 10 }, (_, index) => createEmptyRow(index + 1))
  );

  // State for dropdowns
  const [buildings, setBuildings] = useState<Array<{ id: number, name: string, floors: any[], unitsPerFloor: number }>>([]);
  const [floors, setFloors] = useState<string[]>([]);
  const [unitCategories, setUnitCategories] = useState<Array<{ id: number, name: string }>>([]);
  const [allotteeTypes, setAllotteeTypes] = useState<Array<{ id: number, name: string }>>([]);
  const [loading, setLoading] = useState(false);
  const [fetchingDropdowns, setFetchingDropdowns] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [retryAttempt, setRetryAttempt] = useState(0);
  const maxRetryAttempts = 3;

  // Function to fetch dropdown data (defined outside useEffect, using React useCallback to prevent recreation)
  const fetchDropdownData = React.useCallback(async () => {
    if (retryAttempt >= maxRetryAttempts) {
      setErrorMessage(`Failed to load form data after ${maxRetryAttempts} attempts. Please refresh the page.`);
      setFetchingDropdowns(false);

      return;
    }

    setFetchingDropdowns(true);
    setErrorMessage(null);

    try {
      // Fetch buildings data from API using axios
      try {
        const buildingResponse = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/building/list`,
          {
            withCredentials: true
          });

        const buildingData = buildingResponse.data;

        // Log the response from building list API
        console.log("Building list API response:", buildingResponse);
        console.log("Building data:", buildingData);

        if (buildingData.status === "success" && Array.isArray(buildingData.data)) {
          // Map the API response to our internal format
          const buildingsFromApi = buildingData.data.map(building => ({
            id: building.id,
            name: building.soc_building_name,
            floors: building.floor_array || [],
            unitsPerFloor: building.units_per_floor || 10 // Default to 10 units if not specified
          }));

          setBuildings(buildingsFromApi);

          console.log("Processed buildings with units_per_floor:", buildingsFromApi);
        } else {
          console.error("Invalid building data format:", buildingData);

          // Fallback to empty array if API fails
          setBuildings([]);
        }
      } catch (error) {
        console.error("Error fetching buildings:", error);
        setBuildings([]);
        throw error; // Propagate to retry mechanism
      }

      // Default floors data
      setFloors(["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]);

      // Set default unit categories
      setUnitCategories([
        { id: 1, name: "1 BHK" },
        { id: 2, name: "2 BHK" },
        { id: 3, name: "3 BHK" },
        { id: 4, name: "4 BHK" },
        { id: 5, name: "Studio" },
        { id: 6, name: "Penthouse" }
      ]);

      // Fetch allottee types data from API
      try {
        const allotteeTypeResponse = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/membertype/details`,
          {
            withCredentials: true
          });

        const allotteeTypeData = allotteeTypeResponse.data;

        // Log the response from allottee type API
        console.log("Allottee type API response:", allotteeTypeResponse);
        console.log("Allottee type data:", allotteeTypeData);

        if (allotteeTypeData.status === "success" && Array.isArray(allotteeTypeData.data)) {
          // Map the API response to our internal format
          const allotteeTypesFromApi = allotteeTypeData.data.map(type => ({
            id: type.id,
            name: type.member_type_name || type.name || type.type_name || `Type ${type.id}`
          }));

          setAllotteeTypes(allotteeTypesFromApi);

          console.log("Processed allottee types:", allotteeTypesFromApi);
        } else {
          console.error("Invalid allottee type data format:", allotteeTypeData);

          // Fallback to default allottee types if API fails
          setAllotteeTypes([
            { id: 1, name: "Owner" },
            { id: 2, name: "Tenant" },
            { id: 3, name: "Family Member" },
            { id: 4, name: "Caretaker" }
          ]);
        }
      } catch (error) {
        console.error("Error fetching allottee types:", error);

        // Fallback to default allottee types if API fails
        setAllotteeTypes([
          { id: 1, name: "Owner" },
          { id: 2, name: "Tenant" },
          { id: 3, name: "Family Member" },
          { id: 4, name: "Caretaker" }
        ]);

        // Don't throw error here since this is not critical for the main functionality
      }

    } catch (error) {
      console.error(`Error fetching dropdown data (attempt ${retryAttempt + 1}/${maxRetryAttempts}):`, error);

      if (retryAttempt < maxRetryAttempts - 1) {
        setRetryAttempt(prevAttempt => prevAttempt + 1);
        toast.info(`Failed to load some data. Retrying... (${retryAttempt + 1}/${maxRetryAttempts})`);

        // Try again after a delay
        setTimeout(() => {
          fetchDropdownData();
        }, 2000); // Wait 2 seconds before retrying

        return; // Don't clear fetching status since we're retrying
      } else {
        setErrorMessage("Failed to load form data. Please refresh the page.");
        toast.error("Failed to load form data");
      }
    } finally {
      setFetchingDropdowns(false);
    }
  }, [maxRetryAttempts, retryAttempt]); // Only dependencies that won't change often

  // Fetch dropdown data on component mount only once
  useEffect(() => {
    if (!hasCalledAPI.current) {
      fetchDropdownData();
      hasCalledAPI.current = true;
    }
  }, [fetchDropdownData]);

  const addRow = () => {
    const newRow: AllotteeRow = {
      id: rows.length + 1,
      building: "",
      floor: "",
      unitNumber: "",
      unitCategory: "",
      name: "",
      mobileNo: "",
      email: "",
      allotteeType: "",
      effectiveDate: null,
    };

    setRows([...rows, newRow]);
  };

  const deleteRow = (id: number) => {
    // Only allow deletion if we'll still have at least 10 rows afterward
    // and only allow deleting rows with ID > 10 (rows beyond the initial 10)
    if (rows.length > 10 && id > 10) {
      setRows(rows.filter(row => row.id !== id));
    }
  };

  const updateRow = (id: number, field: keyof AllotteeRow, value: any) => {
    console.log(`Updating row ${id}, field ${field} with value:`, value);
    setRows(prevRows => prevRows.map(row =>
      row.id === id ? { ...row, [field]: value } : row
    ));
  };

  // Email validation function
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    return emailRegex.test(email);
  };

  // Mobile number validation function
  const isValidMobileNumber = (mobile: string): boolean => {
    // Allows for country code format: +91 1234567890 or 1234567890
    const mobileRegex = /^(\+\d{1,3}\s?)?(\d{10})$/;

    return mobileRegex.test(mobile);
  };

  const handleSave = async () => {
    if (loading) return;

    setLoading(true);

    try {
      // Filter out empty rows and validate required fields
      const filledRows = rows.filter(row =>
        row.building && row.floor && row.unitNumber && row.unitCategory &&
        row.name && row.mobileNo && row.email && row.allotteeType && row.effectiveDate
      );

      if (filledRows.length === 0) {
        toast.error("Please fill at least one complete row");
        setLoading(false);
        return;
      }

      // Validate email formats for filled rows
      const invalidEmailRows = filledRows.filter(row => !isValidEmail(row.email));
      
      if (invalidEmailRows.length > 0) {
        toast.error(`Invalid email format in row ${invalidEmailRows[0].id}`);
        setLoading(false);

        return;
      }

      // Validate mobile number formats for filled rows
      const invalidMobileRows = filledRows.filter(row => !isValidMobileNumber(row.mobileNo));

      if (invalidMobileRows.length > 0) {
        toast.error(`Invalid mobile number format in row ${invalidMobileRows[0].id}. Use format: +91 1234567890 or 1234567890`);
        setLoading(false);

        return;
      }

      // Convert the filled rows to API format with proper ID mapping
      const apiData = filledRows.map(row => {
        // Find the building ID from the building name
        const selectedBuilding = buildings.find(b => b.name === row.building);
        const buildingId = selectedBuilding?.id || null;

        // Use the IDs directly since we're now storing the IDs
        const unitCategoryId = row.unitCategory;
        const allotteeTypeId = row.allotteeType;

        return {
          building_id: buildingId,
          soc_building_name: row.building,
          soc_building_floor: row.floor,
          unit_flat_number: row.unitNumber,
          fk_unit_category_id: unitCategoryId,
          name: row.name,
          mobile_no: row.mobileNo,
          email: row.email,
          fk_allottee_type_id: allotteeTypeId,
          allottee_type: allotteeTypes.find(t => t.id.toString() === row.allotteeType)?.name || "",
          effective_date: row.effectiveDate ? dayjs(row.effectiveDate).format('YYYY-MM-DD') : null
        };
      });

      // Function to make API call with retry using axios
      const saveWithRetry = async (retryCount = 0, maxRetries = 2) => {
        try {
          // Debug the data we're sending
          console.log("Sending data to API:", apiData);
          console.log("API URL:", `${process.env.NEXT_PUBLIC_API_URL}/admin/member/bulkAdd`);
          
          const response = await axios.post(`${process.env.NEXT_PUBLIC_API_URL}/admin/member/bulkAdd`, {allottees: apiData}, {
            headers: {
              'Content-Type': 'application/json',
            },
            withCredentials: true,
            timeout: 30000 // Increased timeout to 30 seconds
          });

          console.log("API Response:", response);

          const result = response.data;

          if (result.status === 'success') {
            toast.success(result.message || "Bulk allottees saved successfully!");
            handleCancel(); // Reset form on success
            return true;
          } else {
            throw new Error(result.message || "Failed to save data");
          }
        } catch (error) {
          console.error(`Error saving data (attempt ${retryCount + 1}):`, error);
          console.error("Error details:", {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            config: {
              url: error.config?.url,
              method: error.config?.method,
              data: error.config?.data
            }
          });

          // Get error message from axios error object
          const errorMessage = error.response?.data?.message ||
            error.response?.data?.error ||
            error.message ||
            "Failed to save bulk allottees";

          if (retryCount < maxRetries) {
            // Show retry toast
            toast.info(`Retrying... (${retryCount + 1}/${maxRetries})`);

            // Wait a bit before retrying
            await new Promise(resolve => setTimeout(resolve, 2000));

            return saveWithRetry(retryCount + 1, maxRetries);
          } else {
            // All retries failed
            setErrorMessage(`Failed to save data after ${maxRetries + 1} attempts. Please try again later.`);
            toast.error(errorMessage);

            // Log detailed error for debugging
            console.error("API Error Details:", {
              error: error,
              request: {
                url: `${process.env.NEXT_PUBLIC_API_URL}/admin/member/bulkAdd`,
                method: 'POST',
                dataSize: apiData.length
              },
              response: error.response?.data
            });

            return false;
          }
        }
      };

      // Call the retry function
      await saveWithRetry();
    } catch (error) {
      console.error("Error in save process:", error);
      toast.error((error as Error).message || "Failed to save bulk allottees");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    // Refresh the page
    window.location.reload();
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box sx={{ p: 3 }}>
        {errorMessage && (
          <Alert
            severity="error"
            sx={{ mb: 2 }}
            onClose={() => setErrorMessage(null)}
            action={
              <Button
                color="inherit"
                size="small"
                onClick={() => {
                  setRetryAttempt(0);
                  setErrorMessage(null);
                  fetchDropdownData();
                }}
                disabled={fetchingDropdowns}
              >
                {fetchingDropdowns ? <CircularProgress size={16} /> : "Retry"}
              </Button>
            }
          >
            {errorMessage}
          </Alert>
        )}
        <Card>
          <CardHeader
            title={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h6" component="h2">
                  New Bulk Allottees
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={addRow}
                  size="small"
                  disabled={loading}
                >
                  Add Row
                </Button>
                <Button
                  variant="contained"
                  color="success"
                  startIcon={loading ? <CircularProgress size={16} color="inherit" /> : <SaveIcon />}
                  onClick={handleSave}
                  size="small"
                  disabled={loading}
                >
                  {loading ? 'Saving...' : 'Save'}
                </Button>
                <Button
                  variant="outlined"
                  color="error"
                  startIcon={<CancelIcon />}
                  onClick={handleCancel}
                  size="small"
                  disabled={loading}
                >
                  Cancel
                </Button>
              </Box>
            }
            action={
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={onBackClick}
                size="small"
                disabled={loading}
              >
                Back
              </Button>
            }
          />
          <CardContent>
            <TableContainer component={Paper} sx={{ maxHeight: 600, overflow: 'auto' }}>
              <Table stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ minWidth: 60, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Sr No
                    </TableCell>
                    <TableCell sx={{ minWidth: 150, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Building
                    </TableCell>
                    <TableCell sx={{ minWidth: 100, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Floor
                    </TableCell>
                    <TableCell sx={{ minWidth: 120, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Unit Number
                    </TableCell>
                    <TableCell sx={{ minWidth: 120, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Unit Category
                    </TableCell>
                    <TableCell sx={{ minWidth: 150, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Name
                    </TableCell>
                    <TableCell sx={{ minWidth: 200, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Mobile No (with Country Code)
                    </TableCell>
                    <TableCell sx={{ minWidth: 180, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Email
                    </TableCell>
                    <TableCell sx={{ minWidth: 120, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Allottee Type
                    </TableCell>
                    <TableCell sx={{ minWidth: 150, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Effective Date
                    </TableCell>
                    <TableCell sx={{ minWidth: 80, fontWeight: 'bold', backgroundColor: '#f5f5f5' }}>
                      Action
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {rows.map((row) => {
                    // Check if this row is valid
                    const isRowValid = row.building &&
                      row.floor &&
                      row.unitNumber &&
                      row.unitCategory &&
                      row.name &&
                      row.mobileNo && isValidMobileNumber(row.mobileNo) &&
                      row.email && isValidEmail(row.email) &&
                      row.allotteeType &&
                      row.effectiveDate;

                    return (
                      <TableRow
                        key={row.id}
                        hover
                        sx={{
                          ...(row.id <= 10 ? { backgroundColor: 'rgba(232, 244, 253, 0.3)' } : {}),
                          ...(isRowValid ? { '& td:first-of-type': { borderLeft: '4px solid #4caf50' } } : {})
                        }}
                      >
                        <TableCell>{row.id}</TableCell>

                        {/* Building */}
                        <TableCell>
                          <FormControl fullWidth size="small">
                            <Select
                              value={row.building}
                              onChange={(e) => {
                                console.log("Building selected:", e.target.value);

                                const selectedBuilding = buildings.find(b => b.name === e.target.value);

                                updateRow(row.id, 'building', e.target.value);

                                // Clear floor when building changes
                                updateRow(row.id, 'floor', '');
                              }}
                              displayEmpty
                              renderValue={(selected) => {
                                return selected || "Choose one";
                              }}
                            >
                              <MenuItem value="">Choose one</MenuItem>
                              {buildings.map((building) => (
                                <MenuItem key={building.id} value={building.name}>
                                  {building.name}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </TableCell>

                        {/* Floor */}
                        <TableCell>
                          <FormControl fullWidth size="small">
                            <Select
                              value={row.floor}
                              onChange={(e) => {
                                console.log("Floor selected:", e.target.value);
                                updateRow(row.id, 'floor', e.target.value);

                                // Clear unit number when floor changes to ensure consistency
                                updateRow(row.id, 'unitNumber', '');
                              }}
                              displayEmpty
                              disabled={!row.building} // Disable if no building is selected
                              renderValue={(selected) => {
                                return selected || "Choose One";
                              }}
                            >
                              <MenuItem value="">Choose One</MenuItem>
                              {(() => {
                                // Get floors for the selected building
                                const selectedBuilding = buildings.find(b => b.name === row.building);
                                const buildingFloors = selectedBuilding?.floors || [];

                                return buildingFloors.length > 0
                                  ? buildingFloors.map((floor) => (
                                    <MenuItem key={floor} value={floor.toString()}>
                                      {floor}
                                    </MenuItem>
                                  ))
                                  : floors.map((floor) => (
                                    <MenuItem key={floor} value={floor}>
                                      {floor}
                                    </MenuItem>
                                  ));
                              })()}
                            </Select>
                          </FormControl>
                        </TableCell>

                        {/* Unit Number */}
                        <TableCell>
                          <FormControl fullWidth size="small">
                            <Select
                              value={row.unitNumber}
                              onChange={(e) => {
                                console.log("Unit Number selected:", e.target.value);
                                updateRow(row.id, 'unitNumber', e.target.value);
                                
                                // Auto-assign a unit category based on unit number
                                // For example, units ending with 1-3 could be 1BHK, 4-6 could be 2BHK, etc.
                                // This is just a simple logic example - modify according to your business rules
                                if (e.target.value) {
                                  const unitNum = parseInt(e.target.value.toString().slice(-1));
                                  let categoryId = "";
                                  
                                  if (unitNum >= 1 && unitNum <= 3) {
                                    // 1BHK
                                    categoryId = "1";
                                  } else if (unitNum >= 4 && unitNum <= 7) {
                                    // 2BHK
                                    categoryId = "2";
                                  } else {
                                    // 3BHK
                                    categoryId = "3";
                                  }
                                  
                                  updateRow(row.id, 'unitCategory', categoryId);
                                  console.log(`Auto-assigned category ${categoryId} for unit ${e.target.value}`);
                                }
                              }}
                              displayEmpty
                              disabled={!row.building || !row.floor} // Disable if building or floor not selected
                              renderValue={(selected) => {
                                return selected || "Choose Unit Number";
                              }}
                            >
                              <MenuItem value="">Choose Unit Number</MenuItem>
                              {(() => {
                                // Generate unit numbers based on building and floor
                                if (row.building && row.floor) {
                                  const selectedBuilding = buildings.find(b => b.name === row.building);
                                  const floorNumber = parseInt(row.floor, 10);
                                  const unitPrefix = floorNumber * 100; // e.g., floor 1 -> 101, 102, etc.
                                  const unitsPerFloor = selectedBuilding?.unitsPerFloor || 10;
                                  
                                  console.log(`Generating ${unitsPerFloor} units for building ${row.building} floor ${row.floor}`);
                                  
                                  return Array.from({ length: unitsPerFloor }, (_, i) => {
                                    const unitNumber = `${unitPrefix + (i + 1)}`;

                                    return (
                                      <MenuItem key={`unit-${unitNumber}`} value={unitNumber}>
                                        {unitNumber}
                                      </MenuItem>
                                    );
                                  });
                                }
                                return [];
                              })()}
                            </Select>
                          </FormControl>
                        </TableCell>

                        {/* Unit Category */}
                        <TableCell>
                          <TextField
                            size="small"
                            fullWidth
                            placeholder="Unit Category"
                            value={(() => {
                              if (!row.unitCategory) return "";
                              const category = unitCategories.find(c => c.id.toString() === row.unitCategory);

                              return category ? category.name : "";
                            })()}
                            disabled={true}
                            InputProps={{
                              readOnly: true,
                            }}

                            // Hidden input to store the actual category ID value
                            inputProps={{
                              "data-category-id": row.unitCategory
                            }}
                          />
                        </TableCell>

                        {/* Name */}
                        <TableCell>
                          <TextField
                            size="small"
                            fullWidth
                            placeholder="Enter Name"
                            value={row.name}
                            onChange={(e) => updateRow(row.id, 'name', e.target.value)}
                          />
                        </TableCell>

                        {/* Mobile No */}
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                            <TextField
                              size="small"
                              fullWidth
                              placeholder="Enter Mobile No"
                              value={row.mobileNo}
                              onChange={(e) => updateRow(row.id, 'mobileNo', e.target.value)}
                              error={row.mobileNo !== "" && !isValidMobileNumber(row.mobileNo)}
                              helperText={row.mobileNo !== "" && !isValidMobileNumber(row.mobileNo) ? "Invalid format" : ""}
                            />
                            <Tooltip title="Enter 10-digit mobile number with optional country code (e.g., +91 9876543210 or 9876543210)" arrow>
                              <IconButton size="small" sx={{ ml: 0.5 }}>
                                <HelpOutlineIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>

                        {/* Email */}
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                            <TextField
                              size="small"
                              fullWidth
                              placeholder="Enter Email"
                              type="email"
                              value={row.email}
                              onChange={(e) => updateRow(row.id, 'email', e.target.value)}
                              error={row.email !== "" && !isValidEmail(row.email)}
                              helperText={row.email !== "" && !isValidEmail(row.email) ? "Invalid email" : ""}
                            />
                            <Tooltip title="Enter a valid email address (e.g., <EMAIL>)" arrow>
                              <IconButton size="small" sx={{ ml: 0.5 }}>
                                <HelpOutlineIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </TableCell>

                        {/* Allottee Type */}
                        <TableCell>
                          <FormControl fullWidth size="small">
                            <Select
                              value={row.allotteeType}
                              onChange={(e) => {
                                console.log("Allottee Type selected:", e.target.value);
                                updateRow(row.id, 'allotteeType', e.target.value);
                              }}
                              displayEmpty
                              disabled={fetchingDropdowns}
                              renderValue={(selected) => {
                                if (!selected) return "Choose one";
                                const allotteeType = allotteeTypes.find(t => t.id.toString() === selected);
                                return allotteeType ? allotteeType.name : selected;
                              }}
                            >
                              <MenuItem value="">Choose one</MenuItem>
                              {allotteeTypes.map((type) => (
                                <MenuItem key={type.id} value={type.id.toString()}>
                                  {type.name}
                                </MenuItem>
                              ))}
                            </Select>
                          </FormControl>
                        </TableCell>

                        {/* Effective Date */}
                        <TableCell>
                          <DatePicker
                            value={row.effectiveDate ? dayjs(row.effectiveDate) : null}
                            onChange={(date) => updateRow(row.id, 'effectiveDate', date ? date.toDate() : null)}
                            slotProps={{
                              textField: {
                                size: 'small',
                                fullWidth: true,
                                placeholder: 'dd/mm/yyyy'
                              }
                            }}
                          />
                        </TableCell>

                        {/* Action */}
                        <TableCell>
                          {row.id > 10 && (
                            <IconButton
                              onClick={() => deleteRow(row.id)}
                              color="error"
                              size="small"
                            >
                              <DeleteIcon />
                            </IconButton>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Box>
    </LocalizationProvider>
  );
};

export default NewBulkUnit;