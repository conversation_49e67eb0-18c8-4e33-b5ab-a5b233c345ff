import React, { useEffect, useState } from 'react';

import { <PERSON>, <PERSON>H<PERSON><PERSON>, CardContent, Typography, IconButton, Box } from '@mui/material';
import { Icon } from '@iconify/react';
import axios from 'axios';

interface Attachment {
  id: string;
  name: string;
  url: string;
  downloadName: string;
}

interface AttachmentsCardProps {
  ticketId: string;
}

const AttachmentsCard: React.FC<AttachmentsCardProps> = ({ ticketId }) => {
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [isAttachment, setIsAttachment] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!ticketId) return;
    setLoading(true);
    axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/helpdesk/view_issue/${ticketId}/ticket_details`, {
      withCredentials: true,
      timeout: 10000,
    })
      .then((res) => {
        const data = res.data?.data;

        // Support both old and new attachment formats

        if (data && data.has_attachments === 1 && Array.isArray(data.attachments)) {
          setIsAttachment(true);
          setAttachments(data.attachments.map((a: any) => {
            // If new format (file_name, file_path)
            if (a.file_path) {

              // Always use file_path exactly as returned by the API, no modifications
              const url = a.file_path;

              // Extract display name after '::-' if present, else use file_name or file_path
              let displayName = a.file_name;

              if (!displayName && (url.startsWith('http://') || url.startsWith('https://'))) {
                // Try to extract file name from URL
                const parts = url.split('/');
                displayName = parts[parts.length - 1];
              }

              if (displayName) {
                const sepIndex = displayName.indexOf('::-::');
                
                if (sepIndex !== -1) {
                  displayName = displayName.substring(sepIndex + 5);
                }
              }

              return {
                id: a.id?.toString() || displayName || url,
                name: displayName || url,
                url,
                downloadName: displayName || url,
              };
            }
            
            // Fallback to old format

            return {
              id: a.id?.toString() || a.name,
              name: a.name,
              url: a.s3_url || a.url,
              downloadName: a.name,
            };
          }));
        } else {
          setIsAttachment(false);
          setAttachments([]);
        }
      })
      .catch(() => {
        setIsAttachment(false);
        setAttachments([]);
      })
      .finally(() => setLoading(false));
  }, [ticketId]);

  const handleDownload = async (url: string, name: string) => {
    try {
      // Use the URL exactly as returned by the API (file_path), no modifications
      const apiUrl = url;

      // If any http(s) URL, open directly
      if (/^https?:\/\//.test(apiUrl)) {
        window.open(apiUrl, '_blank');
        return;
      }

      // Otherwise, fetch and download as blob
      const response = await fetch(apiUrl, { credentials: 'include' });
      if (!response.ok) throw new Error('Network response was not ok');
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = name;
      document.body.appendChild(link);
      link.click();
      link.remove();
      setTimeout(() => window.URL.revokeObjectURL(blobUrl), 1000);
    } catch {
      alert('Failed to download file.');
    }
  };

  if (loading || !isAttachment) return null;

  return (
    <Card
      sx={{
        mt: 2,
        borderRadius: 2,
        boxShadow: '0 2px 8px 0 rgba(37, 99, 235, 0.08)',
        background: 'rgba(255,255,255,0.95)',
        backdropFilter: 'blur(8px)',
        border: '1px solid rgba(37, 99, 235, 0.08)',
      }}
    >
      <CardHeader
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Icon icon="ri-attachment-2" style={{ fontSize: 20, color: '#2563eb' }} />
            <Typography variant="subtitle1" sx={{ fontWeight: 600, fontSize: '1rem', mr: 1 }}>
              Attachments
            </Typography>
            <Box sx={{ bgcolor: '#2563eb', color: 'white', borderRadius: '50%', px: 1.2, py: 0.2, fontSize: '0.85rem', fontWeight: 600, minWidth: 24, textAlign: 'center' }}>
              {attachments.length}
            </Box>
          </Box>
        }
        sx={{
          bgcolor: 'rgba(37,99,235,0.05)',
          borderBottom: '1px solid rgba(37,99,235,0.1)',
          px: 2.5,
          py: 1.5,
        }}
      />
      <CardContent sx={{ p: 2.5 }}>
        {attachments && attachments.length > 0 ? (
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            {attachments.map((file) => (
              <IconButton
                key={file.id}
                onClick={() => handleDownload(file.url, file.downloadName || file.name)}
                sx={{
                  bgcolor: 'rgba(37,99,235,0.08)',
                  color: '#2563eb',
                  borderRadius: 2,
                  boxShadow: '0 1px 4px 0 rgba(37,99,235,0.08)',
                  transition: 'all 0.2s',
                  '&:hover': {
                    bgcolor: '#2563eb',
                    color: 'white',
                  },
                  width: 48,
                  height: 48,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: 28,
                  position: 'relative',
                }}
                title={file.name}
              >
                <Icon icon="ri-file-2-line" style={{ fontSize: 28 }} />
              </IconButton>
            ))}
          </Box>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No attachments found.
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

export default AttachmentsCard;
