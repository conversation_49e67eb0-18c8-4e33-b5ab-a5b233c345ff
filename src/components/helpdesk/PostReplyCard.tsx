"use client";

import React, { useState, useRef, useCallback } from 'react';

import {
  <PERSON>,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Tabs,
  Tab,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  IconButton,
  Tooltip,
  Checkbox,
  FormControlLabel,
} from '@mui/material';
import { Icon } from '@iconify/react';
import axios from 'axios';
import { toast } from 'react-toastify';

interface PostReplyCardProps {
  onSubmit: (data: { message: string; status: string; type: 'reply' | 'note' | 'assign' }) => void;
  initialStatus?: string;
  onStatusChange?: (status: string) => void;
  memberDetails?: {
    raised_by_name: string;
  };
  ticketId?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`reply-tabpanel-${index}`}
      aria-labelledby={`reply-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 2 }}>{children}</Box>}
    </div>
  );
};

const PostReplyCard: React.FC<PostReplyCardProps> = ({

  onSubmit,
  initialStatus = 'open',
  onStatusChange,
  memberDetails,
  ticketId,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [message, setMessage] = useState('');
  const [status, setStatus] = useState(initialStatus);
  const [cannedResponse, setCannedResponse] = useState('');
  const [cannedResponses, setCannedResponses] = useState<{ canned_title: string; canned_message: string; id: string }[]>([]);
  const [noteTitle, setNoteTitle] = useState('');
  const [assignee, setAssignee] = useState('');
  const [, setAssigneeType] = useState(''); // We only need the setter, not the value
  const [assigneeOptions, setAssigneeOptions] = useState<any[]>([]);

  console.log(activeTab)

  // Helper to get group type by selected assignee id
  const getAssigneeTypeById = (id: string) => {
    if (!id) return '';
    
    // Debug: Log the assignee options to inspect the structure
    console.log('Assignee Options:', assigneeOptions);
    
    for (const group of assigneeOptions) {
      console.log('Checking group:', group.ledger_account_name, 'with rows:', group.rows);
      
      // Check if the ID exists in this group's rows
      const foundRow = group.rows?.find((row: any) => String(row.id) === String(id));
      
      if (foundRow) {
        console.log('Found row:', foundRow);
        
        // Return 'member' if it's a member group, otherwise 'staff'
        const type = group.ledger_account_name?.toLowerCase().includes('member') 
          ? 'member' 
          : 'staff';
          
        console.log('Determined type:', type);
        
        return type;
      }
    }
    
    console.log('No matching group found for ID:', id);
    
    // Default to staff if not found
    return 'staff';
  };

  React.useEffect(() => {
    // Fetch staff_customer_tree for assign dropdown
    axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/helpdesk/add/staff_customer_tree`, { withCredentials: true })
      .then((res) => {
        if (res.data?.status === 'success' && Array.isArray(res.data.data)) {
          setAssigneeOptions(res.data.data);
        } else {
          setAssigneeOptions([]);
        }
      })
      .catch(() => {
        setAssigneeOptions([]);
      });
  }, []);

  const [assignComment, setAssignComment] = useState('');
  const [fontSize, setFontSize] = useState('12');
  const [fontFamily, setFontFamily] = useState('Normal');
  const [sendMail, setSendMail] = useState(true);

  // Refs for rich text editing
  const editorRef = useRef<HTMLDivElement>(null);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleStatusChange = (event: any) => {
    const newStatus = event.target.value as string;

    setStatus(newStatus);

    if (onStatusChange) {
      onStatusChange(newStatus);
    }
  };

  const handleCannedResponseChange = (event: any) => {
    const selectedId = event.target.value as string;

    setCannedResponse(selectedId);

    // Find the selected canned response (support both canned_message and canned_response keys)
    const selected = cannedResponses.find((item) => item.id === selectedId);

    if (selected) {
      // Prefer canned_response, fallback to canned_message
      const cannedContent = selected.canned_response || selected.canned_message || '';
      setMessage(cannedContent);
      
      if (editorRef.current) {
        editorRef.current.innerHTML = cannedContent;
        const inputEvent = new Event('input', { bubbles: true });
        editorRef.current.dispatchEvent(inputEvent);
      }
    }
  };

  React.useEffect(() => {
    // Fetch canned responses from API
    axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/helpdeskcannedresponse/cannedresponse`, { withCredentials: true })
      .then((res) => {
        if (Array.isArray(res.data)) {
          setCannedResponses(res.data);
        } else if (Array.isArray(res.data?.data)) {
          setCannedResponses(res.data.data);
        }
      })
      .catch(() => {
        setCannedResponses([]);
      });
  }, []);

  // Rich text editor functions
  const execCommand = useCallback((command: string, value?: string) => {
    if (editorRef.current) {
      editorRef.current.focus();
      
      // For better browser compatibility
      try {
        document.execCommand(command, false, value);
      } catch (e) {
        console.warn('execCommand failed:', command, e);
      }
      
      // Update state with the new content
      const content = editorRef.current.textContent || '';
      const htmlContent = editorRef.current.innerHTML || '';
      
      if (activeTab === 2) {
        setAssignComment(content);
      } else {
        setMessage(content);
      }
    }
  }, [activeTab]);

  const handleFontFamilyChange = (event: any) => {
    const newFontFamily = event.target.value;

    setFontFamily(newFontFamily);
    
    if (editorRef.current) {
      editorRef.current.focus();

      switch (newFontFamily) {

        case 'Heading 1':

          execCommand('formatBlock', '<h1>');
          break;
        case 'Heading 2':
          execCommand('formatBlock', '<h2>');
          break;
        default:
          execCommand('formatBlock', '<div>');
          break;
      }
    }
  };

  const handleFontSizeChange = (event: any) => {
    const newSize = event.target.value;

    setFontSize(newSize);
    
    if (editorRef.current) {
      editorRef.current.focus();
      
      // Apply font size using style attribute
      const selection = window.getSelection();

      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);

        if (!range.collapsed) {
          try {
            const span = document.createElement('span');

            span.style.fontSize = `${newSize}px`;
            range.surroundContents(span);
          } catch (e) {
            // Fallback for complex selections
            document.execCommand('fontSize', false, '3');

            // Then update the font elements created
            const fontElements = editorRef.current.querySelectorAll('font[size="3"]');

            fontElements.forEach((el) => {
              const span = document.createElement('span');

              span.style.fontSize = `${newSize}px`;
              span.innerHTML = el.innerHTML;
              el.parentNode?.replaceChild(span, el);
            });
          }
        } else {
          // For new text, set the style on the container
          editorRef.current.style.fontSize = `${newSize}px`;
        }
      }
    }
  };

  // Helper function to clean up HTML content by removing unnecessary paragraph wrapping
  const cleanHtmlContent = (html: string): string => {
    if (!html || !html.trim()) return '';

    // Remove leading/trailing whitespace
    const trimmed = html.trim();

    // Create a temporary div to parse the HTML
    const tempDiv = document.createElement('div');
    
    tempDiv.innerHTML = trimmed;

    // Function to recursively process nodes
    const processNode = (node: ChildNode) => {
      // If it's a text node, leave it as is
      if (node.nodeType === Node.TEXT_NODE) return;
      
      // If it's a P tag with no block-level children, unwrap it
      if (node.nodeName === 'P' && !/^(DIV|P|H[1-6]|UL|OL|LI|BLOCKQUOTE|PRE|TABLE|TR|TD|TH|BR)$/i.test(node.nodeName)) {
        const parent = node.parentNode;
        
        while (node.firstChild) {
          parent?.insertBefore(node.firstChild, node);
        }
        
        parent?.removeChild(node);
        
        return;
      }

      // Process all child nodes
      Array.from(node.childNodes).forEach(processNode);
    };

    // Process the root nodes
    Array.from(tempDiv.childNodes).forEach(processNode);

    // Get the cleaned HTML
    let cleaned = tempDiv.innerHTML;

    // Handle case where content is just whitespace or line breaks
    if (!cleaned.trim() || cleaned === '<br>') {
      return '';
    }

    // Remove any remaining empty paragraph tags
    cleaned = cleaned.replace(/<p[^>]*>\s*<\/p>/g, '');

    // If the content is just a single line break, return empty string
    if (cleaned === '<br>' || cleaned === '&nbsp;') {
      return '';
    }

    return cleaned;
  };

  const handleEditorContent = (e: React.FormEvent<HTMLDivElement>) => {
    const content = e.currentTarget.textContent || '';

    const htmlContent = e.currentTarget.innerHTML || '';

    if (activeTab === 2) {
      setAssignComment(content);
    } else {
      setMessage(content);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Get the actual HTML content from the editor
    const rawHtmlContent = editorRef.current?.innerHTML || '';
    const htmlContent = cleanHtmlContent(rawHtmlContent);
    const textContent = editorRef.current?.textContent || '';

    // API call for Post Reply tab (activeTab === 0)
    if (activeTab === 0 && textContent.trim()) {
      // Example payload
      const payload = {
        ticket_id: ticketId,
        message: htmlContent,
        status,
        send_mail: sendMail,
        type: 'reply'
      };

      axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/helpdesk/post_reply`, payload)
        .then(() => {
          toast.success('Reply posted successfully');
          setMessage('');
          if (editorRef.current) editorRef.current.innerHTML = '';
        })
        .catch((error) => {
          toast.error(error.response?.data?.message || 'Failed to post reply');
        });

      return;
    }

    // API call for Internal Note tab (activeTab === 1)
    if (activeTab === 1 && textContent.trim()) {
      const payload = {
        responder_id: ticketId,
        response_text: noteTitle,
        body: htmlContent,
        type: 'note'
      };

      axios.post(`${process.env.NEXT_PUBLIC_API_URL}/api/helpdesk/post_note`,
        payload,
        {
          withCredentials: true
        })
        .then(() => {
          toast.success('Note saved successfully');
          setMessage('');
          setNoteTitle('');
          if (editorRef.current) editorRef.current.innerHTML = '';
          
          // Refresh the page after a short delay to show the success message
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        })
        .catch((error) => {
          toast.error(error.response?.data?.message || 'Failed to save note');
        });

      return;
    }

    // API call for Assign Issue tab (activeTab === 2)
    if (activeTab === 2 && assignee.trim() && textContent.trim()) {
      const cleanedHtmlContent = cleanHtmlContent(htmlContent);

      const payload = {
        ticket_id: ticketId,
        assignee,
        comment: cleanedHtmlContent,
        type: 'assign'
      };

      axios.post('/api/helpdesk/assign_issue', payload)
        .then(() => {
          toast.success('Issue assigned successfully');
          setAssignee('');
          setAssignComment('');
          if (editorRef.current) editorRef.current.innerHTML = '';
          
          // Refresh the page after a short delay to show the success message
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        })
        .catch((error) => {
          toast.error(error.response?.data?.message || 'Failed to assign issue');
        });

      return;
    }
  };

  const handleReset = () => {
    if(activeTab === 0) {
      setMessage('');
      setCannedResponse('');
    } else if (activeTab === 2) {
      setAssignee('');
      setAssignComment('');
    } else {
      setMessage('');

      if (activeTab === 1) {
        setNoteTitle('');
      }
    }

    if (editorRef.current) {
      editorRef.current.innerHTML = '';
    }
  };

  const formatTools = [
    { icon: 'mdi:format-bold', title: 'Bold', command: 'bold' },
    { icon: 'mdi:format-italic', title: 'Italic', command: 'italic' },
    { icon: 'mdi:format-underline', title: 'Underline', command: 'underline' },
    { icon: 'mdi:format-strikethrough', title: 'Strikethrough', command: 'strikeThrough' },
  ];

  const listTools = [
    { icon: 'mdi:format-list-numbered', title: 'Numbered List', command: 'insertOrderedList' },
    { icon: 'mdi:format-list-bulleted', title: 'Bullet List', command: 'insertUnorderedList' },
    { icon: 'mdi:format-indent-increase', title: 'Increase Indent', command: 'indent' },
    { icon: 'mdi:format-indent-decrease', title: 'Decrease Indent', command: 'outdent' },
  ];

  const alignmentTools = [
    { icon: 'mdi:format-align-left', title: 'Align Left', command: 'justifyLeft' },
    { icon: 'mdi:format-align-center', title: 'Align Center', command: 'justifyCenter' },
    { icon: 'mdi:format-align-right', title: 'Align Right', command: 'justifyRight' },
    { icon: 'mdi:format-align-justify', title: 'Justify', command: 'justifyFull' },
  ];

  const attachmentTools = [
    { icon: 'mdi:link', title: 'Insert Link', command: 'createLink' },
    { icon: 'mdi:image', title: 'Insert Image', command: 'insertImage' },
    { icon: 'mdi:attachment', title: 'Attach File', command: null },
  ];

  const handleAttachmentTool = (command: string | null) => {
    if (command === 'createLink') {
      const url = prompt('Enter URL:');

      if (url) {
        execCommand('createLink', url);
      }
    } else if (command === 'insertImage') {
      const url = prompt('Enter image URL:');
      
      if (url) {
        execCommand('insertImage', url);
      }
    }
  };

  return (
    <Card 
      sx={{
        border: '1px solid rgba(0, 0, 0, 0.08)',
        boxShadow: 'none',
        '&:hover': {
          boxShadow: 'none',
        },
      }}
    >
      
      <CardContent sx={{ px: 3, pb: 3 }}>
        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs 
            value={activeTab} 
            onChange={handleTabChange}
            sx={{
              minHeight: 'auto',
              '& .MuiTabs-indicator': {
                height: 2,
              },
              '& .MuiTab-root': {
                minHeight: 'auto',
                py: 1,
                px: 2,
                fontSize: '0.875rem',
                fontWeight: 500,
                textTransform: 'none',
                color: 'text.secondary',
                '&.Mui-selected': {
                  color: 'primary.main',
                },
              },
            }}
          >
            <Tab 
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box component="span" sx={{ fontSize: '16px' }}>💬</Box>
                  Post Reply
                </Box>
              } 
            />
            <Tab 
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box component="span" sx={{ fontSize: '16px' }}>📝</Box>
                  Post Internal Note
                </Box>
              } 
            />
            <Tab 
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box component="span" sx={{ fontSize: '16px' }}>👤</Box>
                  Assign Issue
                </Box>
              } 
            />
          </Tabs>
        </Box>

        <Box component="form" onSubmit={handleSubmit}>
          {/* Tab Content */}
          <TabPanel value={activeTab} index={0}>
            {/* Reply Tab */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary', fontWeight: 500 }}>
                To:
              </Typography>
              <TextField
                fullWidth
                disabled
                value={memberDetails?.member_name_unit_building_name || ''}
                size="small"
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'transparent',
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.08)',
                    },
                  },
                }}
                slotProps={{
                  input: {
                    startAdornment: (
                      <Box component="span" sx={{ marginRight: '8px', opacity: 0.5, fontSize: '16px' }}>
                        👤
                      </Box>
                    )
                  }
                }}
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={sendMail}
                    onChange={(e) => setSendMail(e.target.checked)}
                    color="primary"
                  />
                }
                label="Send Email Notification"
                sx={{ mb: 1, color: 'text.secondary' }}
              />
            </Box>

            {/* Canned Response */}
            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel sx={{ color: 'text.secondary' }}>
                  Quick Response Templates
                </InputLabel>
                <Select
                  value={cannedResponse}
                  label="Quick Response Templates"
                  onChange={handleCannedResponseChange}
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(0, 0, 0, 0.08)',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <MenuItem value="">
                    <em>Select a template</em>
                  </MenuItem>
                  {cannedResponses.map((item) => (
                    <MenuItem key={item.id} value={item.id}>{item.canned_title}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </TabPanel>

          <TabPanel value={activeTab} index={1}>
            {/* Internal Note Tab */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary', fontWeight: 500 }}>
                Internal Note Title*
              </Typography>
              <TextField
                fullWidth
                size="small"
                value={noteTitle}
                onChange={(e) => setNoteTitle(e.target.value)}
                placeholder="Enter note title"
                variant="outlined"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: 'transparent',
                    '& fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.08)',
                    },
                    '&:hover fieldset': {
                      borderColor: 'rgba(0, 0, 0, 0.15)',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: 'primary.main',
                      borderWidth: 1,
                    },
                  },
                }}
              />
            </Box>
          </TabPanel>

          <TabPanel value={activeTab} index={2}>
            {/* Assign Issue Tab */}
            <Box sx={{ mb: 3 }}>
              <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary', fontWeight: 500 }}>
                Assignee*
              </Typography>
              <FormControl fullWidth size="small">
                <Select
                  value={assignee}
                  onChange={(e) => {
                    const selectedId = e.target.value;
                    
                    setAssignee(selectedId);
                    
                    // Always determine and set the assignee type when selection changes
                    const type = getAssigneeTypeById(selectedId);
                    
                    console.log('Selected assignee type:', type, 'for ID:', selectedId);
                    setAssigneeType(type);
                  }}
                  displayEmpty
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(0, 0, 0, 0.08)',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(0, 0, 0, 0.15)',
                    },
                  }}
                  renderValue={(selected) => {

                    if (!selected) return <em>Select Assignee</em>;

                    for (const group of assigneeOptions) {
                      const found = group.rows?.find((row: any) => String(row.id) === String(selected));

                      if (found) return found.ledger_account_name;
                    }

                    return <em>Select Assignee</em>;
                  }}
                >
                  <MenuItem value="" disabled>
                    <em>Select Assignee</em>
                  </MenuItem>
                  {assigneeOptions.map((group) => [
                    <MenuItem key={group.id} value={group.id} disabled sx={{ fontWeight: 700, opacity: 0.7, fontSize: '13px', pl: 1.5 }}>
                      {group.ledger_account_name}
                    </MenuItem>,
                    ...(group.rows || []).map((row: any) => (
                      <MenuItem key={row.id} value={row.id} sx={{ pl: 3 }}>
                        {row.ledger_account_name}
                      </MenuItem>
                    ))
                  ])}
                </Select>
              </FormControl>
            </Box>
          </TabPanel>

          {/* Rich Text Editor - Common for all tabs */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary', fontWeight: 500 }}>
              {activeTab === 0 ? 'Merge Field' : activeTab === 1 ? 'Note Content' : 'Comment'}
            </Typography>
            
            {/* Rich Text Editor Container */}
            <Box sx={{
              border: '1px solid #e0e0e0',
              borderRadius: 1,
              backgroundColor: '#fff',
            }}>
              {/* Rich Text Editor Toolbar */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.25,
                py: 1,
                px: 1.5,
                borderBottom: '1px solid #e0e0e0',
                backgroundColor: '#f8f9fa',
                borderRadius: '4px 4px 0 0',
                flexWrap: 'wrap',
              }}>
                {/* Font Style Dropdown */}
                <FormControl size="small" sx={{ minWidth: 70, mr: 0.5 }}>
                  <Select
                    value={fontFamily}
                    onChange={handleFontFamilyChange}
                    displayEmpty
                    variant="standard"
                    disableUnderline
                    sx={{
                      fontSize: '12px',
                      '& .MuiSelect-select': {
                        py: 0.25,
                        px: 0.5,
                        fontSize: '12px',
                        backgroundColor: '#fff',
                        border: '1px solid #ddd',
                        borderRadius: '3px',
                        minHeight: 'auto',
                      },
                      '& .MuiSvgIcon-root': {
                        fontSize: '16px',
                      }
                    }}
                  >
                    <MenuItem value="Normal" sx={{ fontSize: '12px' }}>Normal</MenuItem>
                    <MenuItem value="Heading 1" sx={{ fontSize: '12px' }}>Heading 1</MenuItem>
                    <MenuItem value="Heading 2" sx={{ fontSize: '12px' }}>Heading 2</MenuItem>
                  </Select>
                </FormControl>

                {/* Font Size */}
                <FormControl size="small" sx={{ minWidth: 35, mr: 1 }}>
                  <Select
                    value={fontSize}
                    onChange={handleFontSizeChange}
                    variant="standard"
                    disableUnderline
                    sx={{
                      fontSize: '12px',
                      '& .MuiSelect-select': {
                        py: 0.25,
                        px: 0.5,
                        fontSize: '12px',
                        backgroundColor: '#fff',
                        border: '1px solid #ddd',
                        borderRadius: '3px',
                        minHeight: 'auto',
                        textAlign: 'center',
                      },
                      '& .MuiSvgIcon-root': {
                        fontSize: '16px',
                      }
                    }}
                  >
                    <MenuItem value="8" sx={{ fontSize: '12px' }}>8</MenuItem>
                    <MenuItem value="9" sx={{ fontSize: '12px' }}>9</MenuItem>
                    <MenuItem value="10" sx={{ fontSize: '12px' }}>10</MenuItem>
                    <MenuItem value="11" sx={{ fontSize: '12px' }}>11</MenuItem>
                    <MenuItem value="12" sx={{ fontSize: '12px' }}>12</MenuItem>
                    <MenuItem value="14" sx={{ fontSize: '12px' }}>14</MenuItem>
                    <MenuItem value="16" sx={{ fontSize: '12px' }}>16</MenuItem>
                    <MenuItem value="18" sx={{ fontSize: '12px' }}>18</MenuItem>
                  </Select>
                </FormControl>

                {/* Format Tools */}
                {formatTools.map((tool) => (
                  <Tooltip key={tool.title} title={tool.title}>
                    <IconButton 
                      size="small" 
                      onClick={() => execCommand(tool.command)}
                      sx={{ 
                        color: '#666',
                        p: 0.25,
                        minWidth: '24px',
                        width: '24px',
                        height: '24px',
                        border: '1px solid transparent',
                        borderRadius: '3px',
                        fontSize: '11px',
                        fontWeight: 'bold',
                        fontFamily: 'Arial, sans-serif',
                        '&:hover': {
                          backgroundColor: '#e6e6e6',
                          border: '1px solid #b3b3b3',
                          color: '#333',
                        }
                      }}
                    >
                      {tool.command === 'bold' ? (
                        <Box component="span" sx={{ fontWeight: 'bold', fontSize: '12px' }}>B</Box>
                      ) : tool.command === 'italic' ? (
                        <Box component="span" sx={{ fontStyle: 'italic', fontSize: '12px' }}>I</Box>
                      ) : tool.command === 'underline' ? (
                        <Box component="span" sx={{ textDecoration: 'underline', fontSize: '12px' }}>U</Box>
                      ) : tool.command === 'strikeThrough' ? (
                        <Box component="span" sx={{ textDecoration: 'line-through', fontSize: '12px' }}>S</Box>
                      ) : (
                        <Icon icon={tool.icon} style={{ fontSize: '14px' }} />
                      )}
                    </IconButton>
                  </Tooltip>
                ))}
                
                {/* List Tools */}
                {listTools.map((tool) => (
                  <Tooltip key={tool.title} title={tool.title}>
                    <IconButton 
                      size="small"
                      onClick={() => execCommand(tool.command)}
                      sx={{ 
                        color: '#666',
                        p: 0.25,
                        minWidth: '24px',
                        width: '24px',
                        height: '24px',
                        border: '1px solid transparent',
                        borderRadius: '3px',
                        fontSize: '10px',
                        fontWeight: 'bold',
                        '&:hover': {
                          backgroundColor: '#e6e6e6',
                          border: '1px solid #b3b3b3',
                          color: '#333',
                        }
                      }}
                    >
                      {tool.command === 'insertOrderedList' ? (
                        <Box component="span" sx={{ fontSize: '11px', fontWeight: 'bold' }}>1.</Box>
                      ) : tool.command === 'insertUnorderedList' ? (
                        <Box component="span" sx={{ fontSize: '14px', fontWeight: 'bold' }}>•</Box>
                      ) : tool.command === 'indent' ? (
                        <Box component="span" sx={{ fontSize: '12px' }}>→</Box>
                      ) : tool.command === 'outdent' ? (
                        <Box component="span" sx={{ fontSize: '12px' }}>←</Box>
                      ) : (
                        <Icon icon={tool.icon} style={{ fontSize: '14px' }} />
                      )}
                    </IconButton>
                  </Tooltip>
                ))}

                {/* Alignment Tools */}
                {alignmentTools.map((tool) => (
                  <Tooltip key={tool.title} title={tool.title}>
                    <IconButton 
                      size="small"
                      onClick={() => execCommand(tool.command)}
                      sx={{ 
                        color: '#666',
                        p: 0.25,
                        minWidth: '24px',
                        width: '24px',
                        height: '24px',
                        border: '1px solid transparent',
                        borderRadius: '3px',
                        fontSize: '10px',
                        fontWeight: 'bold',
                        '&:hover': {
                          backgroundColor: '#e6e6e6',
                          border: '1px solid #b3b3b3',
                          color: '#333',
                        }
                      }}
                    >
                      {tool.command === 'justifyLeft' ? (
                        <Box component="span" sx={{ fontSize: '12px' }}>⇤</Box>
                      ) : tool.command === 'justifyCenter' ? (
                        <Box component="span" sx={{ fontSize: '12px' }}>⟷</Box>
                      ) : tool.command === 'justifyRight' ? (
                        <Box component="span" sx={{ fontSize: '12px' }}>⇥</Box>
                      ) : tool.command === 'justifyFull' ? (
                        <Box component="span" sx={{ fontSize: '12px' }}>⟺</Box>
                      ) : (
                        <Icon icon={tool.icon} style={{ fontSize: '14px' }} />
                      )}
                    </IconButton>
                  </Tooltip>
                ))}
                
                {/* Additional Tools */}
                {attachmentTools.map((tool) => (
                  <Tooltip key={tool.title} title={tool.title}>
                    <IconButton 
                      size="small"
                      onClick={() => tool.command ? handleAttachmentTool(tool.command) : undefined}
                      sx={{ 
                        color: '#666',
                        p: 0.25,
                        minWidth: '24px',
                        width: '24px',
                        height: '24px',
                        border: '1px solid transparent',
                        borderRadius: '3px',
                        fontSize: '10px',
                        fontWeight: 'bold',
                        '&:hover': {
                          backgroundColor: '#e6e6e6',
                          border: '1px solid #b3b3b3',
                          color: '#333',
                        }
                      }}
                    >
                      {tool.command === 'createLink' ? (
                        <Box component="span" sx={{ fontSize: '12px' }}>🔗</Box>
                      ) : tool.command === 'insertImage' ? (
                        <Box component="span" sx={{ fontSize: '12px' }}>🖼</Box>
                      ) : !tool.command ? (
                        <Box component="span" sx={{ fontSize: '12px' }}>📎</Box>
                      ) : (
                        <Icon icon={tool.icon} style={{ fontSize: '14px' }} />
                      )}
                    </IconButton>
                  </Tooltip>
                ))}

                {/* Table and Code Tools */}
                <Tooltip title="Insert Table">
                  <IconButton 
                    size="small"
                    onClick={() => {
                      const table = '<table border="1" style="border-collapse: collapse; width: 100%;"><tr><td style="border: 1px solid #ddd; padding: 8px;">Cell 1</td><td style="border: 1px solid #ddd; padding: 8px;">Cell 2</td></tr><tr><td style="border: 1px solid #ddd; padding: 8px;">Cell 3</td><td style="border: 1px solid #ddd; padding: 8px;">Cell 4</td></tr></table>';
                    
                      execCommand('insertHTML', table);
                    }}
                    sx={{ 
                      color: '#666',
                      p: 0.25,
                      minWidth: '24px',
                      width: '24px',
                      height: '24px',
                      border: '1px solid transparent',
                      borderRadius: '3px',
                      '&:hover': {
                        backgroundColor: '#e6e6e6',
                        border: '1px solid #b3b3b3',
                        color: '#333',
                      }
                    }}
                  >
                    <Box component="span" sx={{ fontSize: '12px' }}>⊞</Box>
                  </IconButton>
                </Tooltip>
                <Tooltip title="Insert Code">
                  <IconButton 
                    size="small"
                    onClick={() => execCommand('formatBlock', '<pre>')}
                    sx={{ 
                      color: '#666',
                      p: 0.25,
                      minWidth: '24px',
                      width: '24px',
                      height: '24px',
                      border: '1px solid transparent',
                      borderRadius: '3px',
                      '&:hover': {
                        backgroundColor: '#e6e6e6',
                        border: '1px solid #b3b3b3',
                        color: '#333',
                      }
                    }}
                  >
                    <Box component="span" sx={{ fontSize: '12px', fontFamily: 'monospace' }}>{'<>'}</Box>
                  </IconButton>
                </Tooltip>
              </Box>

              {/* Rich Text Editor Content Area */}
              <Box
                ref={editorRef}
                contentEditable
                suppressContentEditableWarning
                onInput={handleEditorContent}
                sx={{
                  minHeight: activeTab === 1 ? '200px' : '120px',
                  p: 2,
                  backgroundColor: '#fff',
                  fontSize: `${fontSize}px`,
                  fontFamily: 'Arial, sans-serif',
                  lineHeight: 1.4,
                  color: '#333',
                  borderRadius: '0 0 4px 4px',
                  '&:focus': {
                    outline: 'none',
                  },
                  '&[contenteditable]:empty:before': {
                    content: `"${
                      activeTab === 0 ? 'Type your reply here...' :
                      activeTab === 1 ? 'Add your internal note content here...' :
                      'Add assignment comment here...'
                    }"`,
                    color: '#999',
                    fontStyle: 'italic',
                  },
                  '& p': {
                    margin: '0 0 8px 0',
                    lineHeight: 1.4,
                  },
                  '& ul, & ol': {
                    margin: '0 0 8px 20px',
                    padding: 0,
                  },
                  '& li': {
                    margin: '2px 0',
                  },
                  '& table': {
                    borderCollapse: 'collapse',
                    width: '100%',
                    margin: '8px 0',
                  },
                  '& td, & th': {
                    border: '1px solid #ddd',
                    padding: '8px',
                    textAlign: 'left',
                  },
                  '& h1': {
                    fontSize: '24px',
                    fontWeight: 'bold',
                    margin: '0 0 12px 0',
                  },
                  '& h2': {
                    fontSize: '20px',
                    fontWeight: 'bold', 
                    margin: '0 0 10px 0',
                  },
                  '& pre': {
                    backgroundColor: '#f4f4f4',
                    padding: '8px',
                    borderRadius: '4px',
                    fontFamily: 'monospace',
                    fontSize: '13px',
                    overflow: 'auto',
                  }
                }}
              />
            </Box>

            {/* Merge Field Helper (only for Reply tab) */}
            {activeTab === 0 && (
              <Box sx={{ mt: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                    Available merge fields:
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                  {[
                    { label: 'Ticket Number', value: ticketId || '{{Ticket ID}}' },
                    { label: 'Name', value: memberDetails?.raised_by_name || '{{Member Name}}' }
                  ].map((field) => (
                    <Button
                      key={field.value}
                      size="small"
                      variant="outlined"
                      onClick={() => {
                        if (editorRef.current) {
                          editorRef.current.focus();
                          execCommand('insertText', field.value);
                        }
                      }}
                      sx={{
                        fontSize: '11px',
                        py: 0.25,
                        px: 1,
                        minHeight: '24px',
                        borderColor: '#ddd',
                        color: '#666',
                        '&:hover': {
                          borderColor: '#999',
                          backgroundColor: '#f5f5f5',
                        }
                      }}
                    >
                      {field.label}
                    </Button>
                  ))}
                </Box>
              </Box>
            )}
          </Box>

          {/* Status Selection */}
          {activeTab === 0 && (
            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth size="small">
                <InputLabel sx={{ color: 'text.secondary' }}>
                  Change Status
                </InputLabel>
                <Select
                  value={status}
                  label="Change Status"
                  onChange={handleStatusChange}
                  sx={{
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(0, 0, 0, 0.08)',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'rgba(0, 0, 0, 0.15)',
                    },
                  }}
                >
                  <MenuItem value="resolved">Resolved</MenuItem>
                  <MenuItem value="closed">Closed</MenuItem>
                  <MenuItem value="on hold">On Hold</MenuItem>
                  
                </Select>
              </FormControl>
            </Box>
          )}

          {/* Action Buttons: Separate Save buttons for each tab */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            {/* Post Reply Tab Save/Reset */}
            {activeTab === 0 && (
              <Box sx={{ display: 'flex', gap: 2, ml: 'auto' }}>
                <Button
                  type="button"
                  variant="contained"
                  color="primary"
                  startIcon={<Box component="span" sx={{ fontSize: '16px' }}>📤</Box>}
                  sx={{
                    boxShadow: 'none',
                    '&:hover': {
                      boxShadow: 'none',
                    },
                  }}
                  onClick={async (e) => {
                    e.preventDefault();

                    // Get the actual HTML content from the editor
                    const rawHtmlContent = editorRef.current?.innerHTML || '';
                    const htmlContent = cleanHtmlContent(rawHtmlContent);
                    const textContent = editorRef.current?.textContent || '';
                    
                    if (textContent.trim()) {
                      let canned_title = undefined;

                      if (cannedResponse) {
                        const selected = cannedResponses.find((item) => item.id === cannedResponse);

                        if (selected) {
                          canned_title = selected.canned_title;
                        }
                      }
                      
                      const payload = {
                        ticket_id: ticketId,
                        canned_response: htmlContent,
                        status,
                        send_mail: sendMail,
                        type: 'reply',
                        ...(canned_title ? { canned_title } : {})
                      };

                      try {
                        await axios.post(
                          `${process.env.NEXT_PUBLIC_API_URL}/admin/helpdesk/view_issue/${ticketId}/post_reply`,
                          payload,
                          { withCredentials: true }
                        );
                        toast.success('Reply posted successfully');
                        setMessage('');
                        if (editorRef.current) editorRef.current.innerHTML = '';

                        // Refresh the page after successful post

                        if (typeof window !== 'undefined') {
                          window.location.reload();
                        }
                      } catch (error) {
                        toast.error(error?.response?.data?.message || 'Failed to post reply');
                      }
                    } else {
                      toast.error('Reply message cannot be empty');
                    }
                  }}
                >
                  Post Reply
                </Button>
                <Button
                  type="button"
                  variant="outlined"
                  color="info"
                  startIcon={<Box component="span" sx={{ fontSize: '16px' }}>🔄</Box>}
                  onClick={handleReset}
                  sx={{
                    borderColor: 'rgba(0, 123, 255, 0.3)',
                    color: 'info.main',
                    '&:hover': {
                      borderColor: 'info.main',
                      backgroundColor: 'rgba(0, 123, 255, 0.04)',
                    },
                  }}
                >
                  Reset
                </Button>
              </Box>
            )}
            {/* Internal Note Tab Save/Reset */}
            {activeTab === 1 && (
              <Box sx={{ display: 'flex', gap: 2, ml: 'auto' }}>
                <Button
                  type="button"
                  variant="contained"
                  color="success"
                  startIcon={<Box component="span" sx={{ fontSize: '16px' }}>💾</Box>}
                  sx={{
                    backgroundColor: 'success.main',
                    boxShadow: 'none',
                    '&:hover': {
                      backgroundColor: 'success.dark',
                      boxShadow: 'none',
                    },
                  }}
                  onClick={async (e) => {
                    e.preventDefault();
                    const rawHtmlContent = editorRef.current?.innerHTML || '';
                    const htmlContent = cleanHtmlContent(rawHtmlContent);
                    const textContent = editorRef.current?.textContent || '';

                    if (textContent.trim()) {
                      if (activeTab === 1) {
                        try {
                          const cleanedHtmlContent = cleanHtmlContent(htmlContent);

                          const payload = {
                            responder_id: ticketId,
                            response_text: noteTitle,
                            body: cleanedHtmlContent,
                            type: 'note'
                          };

                          await axios.post(
                            `${process.env.NEXT_PUBLIC_API_URL}/admin/helpdesk/view_issue/${ticketId}/post_internal_note`,
                            payload,
                            { withCredentials: true }
                          );

                          toast.success('Note saved successfully');
                          setMessage('');
                          setNoteTitle('');
                          if (editorRef.current) editorRef.current.innerHTML = '';
                          
                          // Refresh the page after a short delay to show the success message
                          setTimeout(() => {
                            window.location.reload();
                          }, 1000);
                        } catch (error) {
                          toast.error(error?.response?.data?.message || 'Failed to save note');
                        }
                      }
                    }
                  }}
                >
                  Save Note
                </Button>
                <Button
                  type="button"
                  variant="outlined"
                  color="info"
                  startIcon={<Box component="span" sx={{ fontSize: '16px' }}>🔄</Box>}
                  onClick={handleReset}
                  sx={{
                    borderColor: 'rgba(0, 123, 255, 0.3)',
                    color: 'info.main',
                    '&:hover': {
                      borderColor: 'info.main',
                      backgroundColor: 'rgba(0, 123, 255, 0.04)',
                    },
                  }}
                >
                  Reset
                </Button>
              </Box>
            )}
            {/* Assign Issue Tab Save/Reset */}
            {activeTab === 2 && (
              <Box sx={{ display: 'flex', gap: 2, ml: 'auto' }}>
                <Button
                  type="button"
                  variant="contained"
                  color="success"
                  startIcon={<Box component="span" sx={{ fontSize: '16px' }}>✅</Box>}
                  sx={{
                    backgroundColor: 'success.main',
                    boxShadow: 'none',
                    '&:hover': {
                      backgroundColor: 'success.dark',
                      boxShadow: 'none',
                    },
                  }}
                  onClick={async (e) => {
                    e.preventDefault();
                    const rawHtmlContent = editorRef.current?.innerHTML || '';
                    const htmlContent = cleanHtmlContent(rawHtmlContent);
                    const textContent = editorRef.current?.textContent || '';

                    // Defensive: always treat assignee as string
                    const assigneeId = typeof assignee === 'string' ? assignee : String(assignee);

                    if (assigneeId && assigneeId !== '' && textContent.trim()) {

                      // Get the assignee type for the selected assignee
                      const type = getAssigneeTypeById(assigneeId);
                      
                      console.log('Submitting with assignee type:', type, 'for ID:', assigneeId);
                      
                      // Update the assignee type in state
                      setAssigneeType(type);

                      // Ensure we have a valid type, default to 'staff' if empty
                      const finalType = type || 'staff';
                      
                      const payload = {
                        responder_id: assigneeId,
                        assignee_type: finalType, // Use the determined type (member or staff)
                        assignee: assigneeId,
                        body: htmlContent,
                        type: 'assign',
                        responder_type: finalType,
                      };
                      
                      console.log('Final payload:', payload);

                      try {
                        await axios.post(
                          `${process.env.NEXT_PUBLIC_API_URL}/admin/helpdesk/view_issue/${ticketId}/assign_issue`,
                          payload,
                          { withCredentials: true }
                        );
                        toast.success('Issue assigned successfully');
                        setAssignee('');
                        setAssigneeType('');
                        setAssignComment('');
                        if (editorRef.current) editorRef.current.innerHTML = '';
                        
                        // Refresh the page after a short delay to show the success message
                        setTimeout(() => {
                          window.location.reload();
                        }, 1000);
                      } catch (error) {
                        toast.error(error?.response?.data?.message || 'Failed to assign issue');
                      }
                    } else if (!assigneeId || assigneeId === '') {
                      toast.error('Please select an assignee.');
                    } else if (!textContent.trim()) {
                      toast.error('Comment cannot be empty.');
                    }
                  }}
                >
                  Assign
                </Button>
                <Button
                  type="button"
                  variant="outlined"
                  color="info"
                  startIcon={<Box component="span" sx={{ fontSize: '16px' }}>🔄</Box>}
                  onClick={handleReset}
                  sx={{
                    borderColor: 'rgba(0, 123, 255, 0.3)',
                    color: 'info.main',
                    '&:hover': {
                      borderColor: 'info.main',
                      backgroundColor: 'rgba(0, 123, 255, 0.04)',
                    },
                  }}
                >
                  Reset
                </Button>
              </Box>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default PostReplyCard;