'use client'

import React, { useState, useEffect } from 'react'

import { useRouter } from 'next/navigation'

import {
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Divider,
  Stack,
  CircularProgress,
} from '@mui/material'
import EditIcon from '@mui/icons-material/Edit'
import DownloadIcon from '@mui/icons-material/Download'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import axios from 'axios'

interface Nominee {
  nominees_id: number
  nominee_name: string
  nominee_address: string
  percentage: string
}

interface ShareCertificateData {
  fullName: string
  certificateNo: string
  flatNo: string
  memberRegNo: string
  admissionDate: string
  entranceFee: number
  address: string
  occupation: string
  ageOnAdmission: number
  nominees: Nominee[]
  cessationReason: string
  noOfShares: number
  shareSeriesStart: number
  shareSeriesEnd: number
  shareValue: number
  amountPaid: number
  socBuildingName: string
  unitFlatNumber: string
}

interface ShareCertificateProps {
  memberId: string
}

const ShareCertificate: React.FC<ShareCertificateProps> = ({ memberId }) => {
  const router = useRouter()
  const [certificateData, setCertificateData] = useState<ShareCertificateData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCertificateData = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await axios.get(
          `${process.env.NEXT_PUBLIC_API_URL}/admin/member/viewMemberShares/${memberId}`,
          {
            withCredentials: true // Ensure cookies are sent with the request
          }
        )

        console.log('Full API Response:', response.data)
        console.log('Response Status:', response.data.status)

        if (response.data.status === 'success') {
          const data = response.data.data

          // Log the data to see the structure
          console.log('API Response Data:', data)

          setCertificateData({
            fullName: data.full_name || '-',
            certificateNo: data.certificate_no || '-',
            flatNo: data.unit_flat_number || data.unit_id?.toString() || '-',
            memberRegNo: data.member_reg_no || '-',
            admissionDate: data.admission_date ? new Date(data.admission_date).toLocaleDateString() : '-',
            entranceFee: data.entrance_fee || 0,
            address: data.address || '-',
            occupation: data.occupation || '-',
            ageOnAdmission: data.age_on_admission || 0,
            nominees: data.nominees || [],
            cessationReason: data.ceasing_reason || '-',
            noOfShares: data.no_of_shares || 0,
            shareSeriesStart: data.share_series_start || 0,
            shareSeriesEnd: data.share_series_end || 0,
            shareValue: data.share_value || 0,
            amountPaid: data.amount_paid || 0,
            socBuildingName: data.soc_building_name || '',
            unitFlatNumber: data.unit_flat_number || ''
          })
        } else {
          // Set empty data instead of error
          setCertificateData({
            fullName: '-',
            certificateNo: '-',
            flatNo: '-',
            memberRegNo: '-',
            admissionDate: '-',
            entranceFee: 0,
            address: '-',
            occupation: '-',
            ageOnAdmission: 0,
            nominees: [],
            cessationReason: '-',
            noOfShares: 0,
            shareSeriesStart: 0,
            shareSeriesEnd: 0,
            shareValue: 0,
            amountPaid: 0,
            socBuildingName: '',
            unitFlatNumber: ''
          })
        }
      } catch (err) {
        console.error('Error fetching certificate data:', err)

        // Set empty data instead of error

        setCertificateData({
          fullName: '-',
          certificateNo: '-',
          flatNo: '-',
          memberRegNo: '-',
          admissionDate: '-',
          entranceFee: 0,
          address: '-',
          occupation: '-',
          ageOnAdmission: 0,
          nominees: [],
          cessationReason: '-',
          noOfShares: 0,
          shareSeriesStart: 0,
          shareSeriesEnd: 0,
          shareValue: 0,
          amountPaid: 0,
          socBuildingName: '',
          unitFlatNumber: ''
        })
      } finally {
        setLoading(false)
      }
    }

    if (memberId) {
      fetchCertificateData()
    }
  }, [memberId])

  const handleEdit = () => {
    // Navigate to edit page with memberId
    router.push(`/admin/society/member/addMemberShares/${memberId}/`)
  }

  const handleDownload = () => {
    console.log('Download functionality')
  }

  const handleBack = () => {
    router.back()
  }

  // Function to check if all data except flatNo is empty/0
  const isDataEmpty = (data: ShareCertificateData) => {
    return (
      (!data.fullName || data.fullName === '-') &&
      (!data.certificateNo || data.certificateNo === '-') &&
      (!data.memberRegNo || data.memberRegNo === '-') &&
      (!data.admissionDate || data.admissionDate === '-') &&
      data.entranceFee === 0 &&
      (!data.address || data.address === '-') &&
      (!data.occupation || data.occupation === '-') &&
      data.ageOnAdmission === 0 &&
      data.nominees.length === 0 &&
      (!data.cessationReason || data.cessationReason === '-') &&
      data.noOfShares === 0 &&
      data.shareSeriesStart === 0 &&
      data.shareSeriesEnd === 0 &&
      data.shareValue === 0 &&
      data.amountPaid === 0
    )
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress />
      </Box>
    )
  }

  // Always show the page, even if there's no data
  const displayData = certificateData || {
    fullName: '-',
    certificateNo: '-',
    flatNo: '-',
    memberRegNo: '-',
    admissionDate: '-',
    entranceFee: 0,
    address: '-',
    occupation: '-',
    ageOnAdmission: 0,
    nominees: [],
    cessationReason: '-',
    noOfShares: 0,
    shareSeriesStart: 0,
    shareSeriesEnd: 0,
    shareValue: 0,
    amountPaid: 0,
    socBuildingName: '',
    unitFlatNumber: ''
  }

  return (
    <Box sx={{ p: 3, maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant='h4' component='h1' sx={{ fontWeight: 'bold' }}>
          Share Certificate
        </Typography>
        <Stack direction='row' spacing={2}>
          <Button variant='contained' startIcon={<EditIcon />} onClick={handleEdit} sx={{ bgcolor: '#1976d2' }}>
            Edit
          </Button>
          <Button 
            variant='contained' 
            startIcon={<DownloadIcon />} 
            onClick={handleDownload} 
            disabled={isDataEmpty(displayData)}
            sx={{ bgcolor: '#2196f3' }}
          >
            Download
          </Button>
          <Button variant='contained' startIcon={<ArrowBackIcon />} onClick={handleBack} sx={{ bgcolor: '#666' }}>
            Back
          </Button>
        </Stack>
      </Box>

      <Card sx={{ boxShadow: 3 }}>
        <CardContent sx={{ p: 4 }}>
          {/* Form Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Typography variant='h3' component='h2' sx={{ fontWeight: 'bold', mb: 1 }}>
              FORM I
            </Typography>
            <Typography variant='h6' sx={{ mb: 2, color: 'text.secondary' }}>
              Register of Members
            </Typography>
            <Typography variant='h5' sx={{ fontWeight: 'bold' }}>
              Share Certificate - {(() => {
                
                if (displayData.socBuildingName && displayData.unitFlatNumber) {
                  return `${displayData.socBuildingName} ${displayData.unitFlatNumber}`
                } else if (displayData.flatNo !== '-') {
                  return displayData.flatNo
                } else {
                  return 'No Data'
                }
              })()}
            </Typography>
          </Box>

          {/* Certificate Details Table */}
          <TableContainer component={Paper} sx={{ mb: 4, boxShadow: 1 }}>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5', width: '25%' }}>Full Name</TableCell>
                  <TableCell sx={{ width: '25%' }}>{displayData.fullName}</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5', width: '25%' }}>Certificate No</TableCell>
                  <TableCell sx={{ width: '25%' }}>{displayData.certificateNo}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5' }}>Flat No</TableCell>
                  <TableCell>{displayData.flatNo}</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5' }}>Member Reg No</TableCell>
                  <TableCell>{displayData.memberRegNo}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5' }}>Admission Date</TableCell>
                  <TableCell>{displayData.admissionDate}</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5' }}>Entrance Fee</TableCell>
                  <TableCell>₹{displayData.entranceFee.toLocaleString()}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5' }}>Address</TableCell>
                  <TableCell>{displayData.address}</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5' }}>Occupation</TableCell>
                  <TableCell>{displayData.occupation}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5' }}>Age On Admission</TableCell>
                  <TableCell>{displayData.ageOnAdmission}</TableCell>
                  <TableCell sx={{ fontWeight: 'bold', bgcolor: '#f5f5f5' }}>Cessation Reason</TableCell>
                  <TableCell>{displayData.cessationReason}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </TableContainer>

          {/* Nominees Section */}
          {displayData.nominees.length > 0 && (
            <>
              <Typography variant='h5' component='h3' sx={{ mb: 2, textAlign: 'center', fontWeight: 'bold' }}>
                Nominees
              </Typography>
              <TableContainer component={Paper} sx={{ mb: 4, boxShadow: 1 }}>
                <Table>
                  <TableHead>
                    <TableRow sx={{ bgcolor: '#f5f5f5' }}>
                      <TableCell sx={{ fontWeight: 'bold' }}>Nominee Name</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Address</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Percentage</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {displayData.nominees.map((nominee, index) => (
                      <TableRow key={index}>
                        <TableCell>{nominee.nominee_name}</TableCell>
                        <TableCell>{nominee.nominee_address}</TableCell>
                        <TableCell>{nominee.percentage}%</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </>
          )}

          <Divider sx={{ my: 4 }} />

          {/* Particulars of shares held section */}
          <Box sx={{ mt: 4 }}>
            <Typography variant='h5' component='h3' sx={{ mb: 3, textAlign: 'center', fontWeight: 'bold' }}>
              Particulars of shares held
            </Typography>

            <TableContainer component={Paper} sx={{ boxShadow: 2 }}>
              <Table>
                <TableHead>
                  <TableRow sx={{ bgcolor: '#f5f5f5' }}>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>No Of Shares</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>Share Serial Numbers</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>Share Value</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>Total Amount</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell sx={{ textAlign: 'center' }}>{displayData.noOfShares}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>
                      {displayData.shareSeriesStart > 0 && displayData.shareSeriesEnd > 0 
                        ? `${displayData.shareSeriesStart}-${displayData.shareSeriesEnd}` 
                        : '-'}
                    </TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>₹{displayData.shareValue.toLocaleString()}</TableCell>
                    <TableCell sx={{ textAlign: 'center' }}>₹{displayData.amountPaid.toLocaleString()}</TableCell>
                  </TableRow>
                  {/* Total Row */}
                  <TableRow sx={{ bgcolor: '#e3f2fd' }}>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>{displayData.noOfShares}</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>Total</TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>
                      ₹{displayData.shareValue.toLocaleString()}
                    </TableCell>
                    <TableCell sx={{ fontWeight: 'bold', textAlign: 'center' }}>
                      ₹{displayData.amountPaid.toLocaleString()}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </CardContent>
      </Card>
    </Box>
  )
}

export default ShareCertificate
