// Type definitions for Share Certificate component

export interface ShareDetails {
  noOfShares: string;
  shareSerialNumbers: string;
  shareValue: string;
  totalAmount: string;
}

export interface MemberDetails {
  fullName: string;
  certificateNo: string;
  flatNo: string;
  admissionDate: string;
  entranceFee: string;
  address: string;
  occupation: string;
  ageOnAdmission: string;
  nomineeName: string;
  nomineeAddress: string;
  cessationReason: string;
}

// Updated API Response interfaces to match your actual API
export interface ApiMemberShareData {
  id: number;
  certificate_no: string;
  member_reg_no: string;
  unit_id: number;
  full_name: string;
  address: string | null;
  occupation: string | null;
  age_on_admission: number;
  nominee_name: string | null;
  nominee_address: string | null;
  ceasing_reason: string | null;
  no_of_shares: number;
  share_series_start: number;
  share_series_end: number;
  share_value: number;
  amount_paid: number;
  admission_date: string | null;
  entrance_fee: number;
}

export interface ApiNominee {
  nominees_id: number;
  nominee_name: string;
  nominee_address: string;
  percentage: string;
}

export interface ApiResponse {
  status: string;
  status_code: number;
  message: string;
  data: {
    member_share_certificate: ApiMemberShareData;
    iregister: ApiMemberShareData;
    nominees: ApiNominee[];
    cashbookfolios: any[];
  };
}

export interface ShareCertificateProps {
  memberId?: string;
  memberData?: MemberDetails;
  shareData?: ShareDetails;
  societyName?: string;
  formTitle?: string;
  apiBaseUrl?: string;
  onEdit?: () => void;
  onDownload?: () => void;
  onBack?: () => void;
}

export interface MemberField {
  label: string;
  value: string;
}

export interface ShareTableRow {
  noOfShares: string;
  shareSerialNumbers: string;
  shareValue: string;
  totalAmount: string;
}
