import type { VerticalMenuDataType } from '@/types/menuTypes'

const DCOMenu: VerticalMenuDataType[] = [
  {
    label: 'Configure',
    icon: 'ri-building-line',
    children: [
      {
        label: 'Society website',
        href: '/admin/society/socweb/socprofile',
        pageType: 'socprofile',
      },
      {
        label: 'Documents',
        href: '/admin/society/socweb/socprofile/documents',
        pageType: 'socprofile',
      },
      {
        label: 'Allotees',
        href: '/admin/society/member/list',
        pageType: 'table',
        "children": [
          {
            label: 'Allotees',
            href: '/admin/society/member/register',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Register Allottee',
              buttons: [
                { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
              ]
            },
            order: [
              {
                type: 'form',
              },
              {
                type: "helpNote",
                customContent: {
                  title: "Allottee Registration Guidelines:",
                  items: [
                    {
                      primary: "◉ Primary Member:",
                      secondary: "The person who is the first person mentioned in the property registration agreement. This person is liable to pay the maintenance fee, property tax, parking charges and all the chargeable activities done within the society."
                    },
                    {
                      primary: "◉ Associate Member:",
                      secondary: "The person who is allotted one or more units in the society in partnership with the Primary Member and is not the first person mentioned in the property registration agreement."
                    },
                    {
                      primary: "◉ Nominal Member:",
                      secondary: "A occupant who is a family member of the Primary and/or Associate member(s) or a person who is the care-taker on behalf of the Primary Member for a unit in the society."
                    },
                    {
                      primary: "◉ Tenant:",
                      secondary: "A person who has taken the property on lease."
                    }
                  ]
                }
              }
            ]
          },
          {
            label: 'New Bulk Allottees ',
            href: '/admin/society/member/bulkAdd',
            pageType: 'newBulkUnit'
          },
          {
            label: 'Allottee Types',
            href: '/admin/society/membertype/details',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Allottee Types',
              buttons: [
                { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
              ]
            },
            order: [
              {
                type: 'form',
              },
              {
                type: 'table',
              },
              {
                type: 'helpNote',
                customContent: {
                  title: 'Allottee Types Guidelines:',
                  items: [
                    {
                      primary: '◉ Primary Member:',
                      secondary: 'Click on Allottee Type to edit'
                    }
                  ]
                }
              }
            ]
          },
          {
            label: 'Allottee Details',
            href: '/admin/society/member/viewDetails/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Allottee Details',
              buttons: [
                { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
              ]
            },
            order: [
              {
                type: 'card',
                mainGrid: { xs: 12, md: 12, lg: 12 },
                apiURL: '/admin/member/viewDetails/card/[id]',
                headerKeyDisplay: [
                  { key: 'display_name', label: '', isLink: false, isCheckbox: false },
                ],
                keyDisplay: [

                  { key: 'member_email_id', label: 'Email', keyGrid: 3, valueGrid: 9 },
                  { key: 'member_gender', label: 'Gender', keyGrid: 3, valueGrid: 3 },
                  { key: 'member_mobile_number', label: 'Mobile No', keyGrid: 3, valueGrid: 3 },
                  { key: 'intercom', label: 'Intercom', keyGrid: 3, valueGrid: 3 },
                  { key: 'member_dob', label: 'Date of Birth', keyGrid: 3, valueGrid: 3 },
                  { key: 'member_type_name', label: 'Member Type', keyGrid: 3, valueGrid: 3 },,
                  { key: 'unit_type', label: 'Unit Type', keyGrid: 3, valueGrid: 3 },
                ]
              },
              {
                type: 'table',
              }
            ]
          },
          {
            label: 'New / Edit Allottee',
            href: '/admin/society/member/register/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'New / Edit Allottee',
              buttons: [
                { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
              ]
            },
            order: [
              {
                type: 'form',
              },
              {
                type: 'helpNote',
                customContent: {
                  title: 'Allottee Reports Guidelines:',
                  items: [
                    {
                      primary: '◉ Primary Member:',
                      secondary: 'The person who is the first person mentioned in the property registration agreement. This person is liable to pay the maintenance fee, property tax, parking charges and all the chargeable activities done within the society.'
                    },
                    {
                      primary: '◉ Associate Member:',
                      secondary: 'The person who is allotted one or more units in the society in partnership with the Primary Member and is not the first person mentioned in the property registration agreement.'
                    },
                    {
                      primary: '◉ Nominal Member:',
                      secondary: 'A occupant who is a family member of the Primary and/or Associate member(s) or a person who is the care-taker on behalf of the Primary Member for a unit in the society.'
                    },
                    {
                      primary: '◉ Tenant:',
                      secondary: 'A person who has taken the property on lease.'
                    }
                  ]
                }
              }
            ]
          },
          {
            label: 'Revoke Allocation of Member ',
            href: '/admin/society/member/delete/[id]',
          },
          {
            label: 'Unit Transfer',
            href: '/admin/society/member/unitTransfer/[id]'
          },
          {
            label: 'Unit Transfer History',
            href: "admin/society/member/delete/[id]"
          },
          {
            label: 'Share Certificates',
            href: '/admin/society/member/viewMemberShares/[id]',
            pageType: 'shareCertificates',
          },
          {
            label:"New / Edit Share Certificates",
            href:"admin/society/member/addMemberShares/[id]"
          }
        ]
      },
      {
        label: 'Vehicles Registration',
        href: '/admin/society/parking/list',
        pageType: 'table',
      },
      {
        label: 'Edit Vehicles Registration',
        href: '/admin/society/parking/registervehicle/[id]',
        pageType: 'table',
      },
      {
        label: 'Parking Allotments ',
        href: '/admin/society/parking-allotments/list',
        pageType: 'table',
        children: [
          {
            label: 'Parking Allotments ',
            href: '/admin/society/parking-allotments/add',
          },
          {
            label: 'Vehicles Registration',
            href: '/admin/society/parking/list',
            pageType: 'table',
            children: [
              {
                label: 'New Vehicles Registration',
                href: '/admin/society/parking/registervehicle',
                pageType: 'mix',
                commonPageConfig: {
                  title: 'New Vehicles Registration',
                  buttons: [
                    { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
                  ]
                },
                order: [
                  {
                    type: 'vehicleRegistration'
                  }
                ]
              }
            ]
          },
          {
            label: 'New / Edit Allocate Parking',
            href: '/admin/society/parking-allotments/add/[id]',
            pageType: 'mix',
            commonPageConfig: {
              title: 'Allocate Parking',
              buttons: [
                { label: 'Back', goBack: true, icon: 'ri-arrow-go-back-fill', color: 'secondary' },
              ]
            },
            order: [
              {
                type: 'form',
              },
              {
                type: "helpNote",
                customContent: {
                  title: "Parking Allocation Guidelines:",
                  items: [
                    {
                      primary: "◉ Parking can be alloted to Primary units(Flats,shops,etc).",
                      secondary: ""
                    },
                    {
                      primary: "◉ Multiple Parkings can be alloted to a primary unit",
                      secondary: ""
                    }
                  ]
                }
              }
            ]

          },
          {
            label: 'Revoke Allocation of Vehicle ',
            href: '/admin/society/parking-allotments/delete/[id]',
          }
        ]
      },
      {
        label: 'Management Committee',
        href: '/admin/society/committees/list',
        pageType: 'table',
        children: [
          {
            label: 'Management Committee',
            href: '/admin/society/committees/add',
          },
          {
            label: 'Form a committee',
            href: '/admin/society/committees/panel/committeeMembers/[id]',
            pageType: 'table',
          },
        ]
      },
      {
        label: 'Noc Forms',
        href: '/admin/society/noc/list',
        pageType: 'table',
        children: [
          {
            label: 'Noc Forms',
            href: '/admin/society/noc/add_noc',
          },
          {
            label: 'NOC',
            href: '/admin/society/noc/add_noc/[id]',
          }
        ]
      },
      {
        label: 'Noc Form Templates',
        href: '/admin/society/societies/nocForms',
        pageType: 'nocForm',
      },
      {
        label: 'Notices & Circulars',
        href: '/admin/society/notices/list',
        pageType: 'table',
        children: [
          {
            label: 'Notices & Circulars',
            href: '/admin/society/notices/add_notice',
          },
        ]
      },
      {
        label: 'Notice & Circular Templates',
        href: '/admin/society/notices/list_template',
        pageType: 'table',
        children: [
          {
            label: 'Notice & Circular Templates',
            href: '/admin/society/notices/templates',
          },
          {
            label: 'Notice & Circular Templates',
            href: '/admin/society/notices/templates/[id]',
          }
        ]
      },
      {
        label: 'Non Member Master',
        href: '/admin/society/income-details/nonmemberMaster',
        pageType: 'table',
        children: [
          {
            label: 'New Non Member Master',
            href: '/admin/society/income-details/addnonmemberMaster',
          },
          {
            label: 'Edit Non Member Master',
            href: '/admin/society/income-details/addnonmemberMaster/[id]',
          }
        ]
      },
      {
        label: 'Inventory',
        href: '/admin/society/inventory/listInventory',
        pageType: 'table',
        children: [
          {
            label: 'New Inventory',
            href: '/admin/society/inventory/addInventoryItem',
          },
          {
            label: 'Inventory Categories',
            href: '/admin/society/inventory/settings',
          }
        ]
      }
    ]
  }
]

export const getDCOMenu = (): VerticalMenuDataType[] => DCOMenu
