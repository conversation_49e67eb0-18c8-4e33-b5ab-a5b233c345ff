{"meta": {"title": "Revoke Allocation of Member"}, "schema": {"facts": [{"fact": "details", "path": "/admin/member/viewDetails/card/:id", "method": "GET", "dependsOn": "id"}], "rules": [{"conditions": {"all": [{"fact": "details", "path": "member_type_name", "operator": "notEqual", "value": "Primary"}, {"fact": "details", "path": "display_name", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"allottee_type": {"__title": "Membership will be cancelled for {{member_name}}"}, "member_name": {"fact": "details", "path": "display_name"}}}]}], "actions": [{"title": "Revoke", "type": "submit", "api_path": "member/delete", "method": "delete", "redirect": "/admin/society/member/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/member/list"}], "flow": {"children": {"allottee_type": {"title": "Impact this action could cause", "default": "Unit will be vacant", "disabled": true}, "cancellation_date": {"title": "Cancel Allotment w.e.f.", "type": "date", "required": true}, "cancellation_reason": {"title": "Cancellation Reason", "type": "string", "required": true, "placeholder": "Enter reason for revoking"}}}}}