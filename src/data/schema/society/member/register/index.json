{"meta": {"title": "Add <PERSON>e"}, "schema": {"facts": [{"fact": "flatList", "path": "/admin/units/list?building_id=[id]&soc_building_floor=[member_building_floor]", "method": "GET", "dependsOn": "soc_building_id", "xpath": "id", "params": {"soc_building_id": {"fact": "soc_building_id", "path": "id"}}, "allowOn": {"member_building_floor": {"fact": "member_building_floor", "operator": "isNumber"}}}], "rules": [{"conditions": {"any": [{"fact": "soc_building_id", "path": "id", "operator": "truthy"}]}, "event": [{"type": "uiSchemaReplace", "params": {"member_building_floor.ui:disabled": false}}]}, {"conditions": {"any": [{"fact": "member_building_floor", "operator": "isNumber"}]}, "event": [{"type": "uiSchemaReplace", "params": {"fk_unit_id.ui:disabled": false}}]}, {"conditions": {"all": [{"fact": "soc_building_id", "path": "floor_array", "operator": "isArray"}]}, "event": [{"type": "schemaOverride", "params": {"member_building_floor.enum": {"fact": "soc_building_id", "path": "floor_array"}}}, {"type": "uiSchemaReplace", "params": {"member_building_floor.ui:enumNames": {"fact": "soc_building_id", "path": "floor_array"}}}]}, {"conditions": {"all": [{"fact": "soc_building_id", "path": "id", "operator": "truthy"}, {"fact": "member_building_floor", "operator": "isNumber"}, {"fact": "flatList", "operator": "isArray"}]}, "event": {"type": "uiSchemaReplace", "params": {"fk_unit_id.ui:options.loadedOptions": {"fact": "flatList"}}}}, {"conditions": {"all": [{"fact": "fk_unit_id", "path": "id", "operator": "truthy"}]}, "event": {"type": "update", "params": {"unit_category": {"fact": "fk_unit_id", "path": "unit_category"}}}}], "actions": [{"title": "Save", "type": "submit", "api_path": "member/register", "redirect": "/admin/society/member/list"}, {"title": "Save & New", "type": "submit_and_new", "api_path": "member/register", "redirect": "/admin/member/register"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/member/list"}], "flow": {"children": {"personal_details": {"title": "Personal Details", "type": "null", "hideLabel": false, "variant": "h5"}, "salute": {"title": "Salute", "type": "select", "enum": [{"const": "mr", "title": "Mr."}, {"const": "ms", "title": "Ms."}, {"const": "mrs", "title": "Mrs."}, {"const": "miss", "title": "Miss"}, {"const": "mx", "title": "Mx."}], "placeholder": "Select Salute"}, "member_first_name": {"title": "First Name", "type": "string", "required": true, "placeholder": "Enter First Name"}, "member_last_name": {"title": "Last Name", "type": "string", "required": true, "placeholder": "Enter Last Name"}, "member_email_id": {"title": "Email Address", "type": "string", "format": "email", "placeholder": "<PERSON><PERSON>"}, "member_mobile_number": {"title": "Mobile Number (with Country Code)", "type": "number", "required": true, "placeholder": "Enter Mobile Number (with Country Code)", "pattern": "[6-9]{1}[0-9]{9}"}, "member_intercom": {"title": "Intercom", "type": "string", "placeholder": "Enter Address"}, "gstin": {"title": "GSTIN", "type": "string", "placeholder": "Enter GSTIN/PAN"}, "ITS_no": {"title": "ITS No", "type": "string", "placeholder": "Enter Unique Code"}, "effective_date": {"title": "Effective Date", "type": "date", "required": true}, "member_dob": {"title": "Date of Birth", "type": "date"}, "member_gender": {"title": "Gender", "type": "radio", "enum": [{"const": "M", "title": "Male"}, {"const": "F", "title": "Female"}]}, "member_type_id": {"title": "Member Type", "type": "dropdown", "placeholder": "Select Member Type", "apiPath": "/admin/membertype/details", "labelKeys": ["member_type_name"], "onMount": true, "required": true}, "unit_details_title": {"title": "Unit Details", "type": "null", "hideLabel": false, "variant": "h5"}, "soc_building_id": {"title": "Building Name", "type": "dropdown", "required": true, "placeholder": "Select Building Tower", "apiPath": "/admin/building/list", "labelKeys": ["soc_building_name"], "onMount": true, "dependent": [{"field": "floor_array"}]}, "member_building_floor": {"title": "Floor No", "type": "select", "placeholder": "Select Floor", "enum": [{"const": null, "title": "Select Floor"}], "minItems": 0, "disabled": true}, "fk_unit_id": {"title": "Unit", "type": "dropdown", "required": true, "labelKeys": ["unit_flat_number"], "placeholder": "Select Units", "disabled": true, "dependent": [{"field": "unit_category"}]}, "unit_category": {"title": "Unit Category", "type": "string", "placeholder": "Select Unit Category", "required": true, "disabled": true}, "is_occupied": {"title": "Is Owner Occupied", "type": "radio", "enum": [{"const": "1", "title": "Yes"}, {"const": "0", "title": "No"}]}}}}}