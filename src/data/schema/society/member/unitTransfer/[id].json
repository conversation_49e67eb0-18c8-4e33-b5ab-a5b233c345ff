{"meta": {"title": "Transfer Flat - Tower B 0000"}, "schema": {"rules": [{"conditions": {"any": [{"fact": "Committee", "path": "committee_type", "operator": "equal", "value": "associate"}]}, "event": [{"type": "remove", "params": {"field": ["members", "unit_deatils"]}}]}, {"conditions": {"any": [{"fact": "Committee", "path": "committee_type", "operator": "equal", "value": "new_member"}]}, "event": [{"type": "remove", "params": {"field": ["nominee", "floor_no"]}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "admin/member/unitTransfer/save", "redirect": "/admin/member/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/member/unitTransfer/list"}], "flow": {"children": {"Committee": {"title": "Select Option of Transfer", "layout": "group", "children": {"committee_type": {"title": "Transfer Unit To", "type": "radio", "required": true, "enum": [{"title": "New Member", "const": "new_member"}, {"title": "Associate / Nominee / Tenant", "const": "associate"}], "default": "new_member"}}}, "members": {"title": "Personal Details ", "layout": "group", "children": {"salute": {"title": "Salute", "type": "select", "enum": [{"const": "mr", "title": "Mr."}, {"const": "mrs", "title": "Mrs."}, {"const": "ms", "title": "Ms."}, {"const": "dr", "title": "Dr."}], "placeholder": "Select Salute"}, "first_name": {"title": "First Name", "type": "string", "required": true, "placeholder": "Enter First Name"}, "last_name": {"title": "Last Name", "type": "string", "required": true, "placeholder": "Enter Last Name"}, "email": {"title": "Email Address", "type": "string", "placeholder": "Enter Email Address"}, "mobile": {"title": "Mobile Number", "type": "string", "placeholder": "Enter Mobile Number"}, "dtaeob": {"title": "Intercom", "type": "date", "placeholder": "Enter Date Of Birth"}, "gstin": {"title": "GSTIN", "type": "string", "placeholder": "Enter GSTIN"}, "members": {"title": "Member w.e.f", "type": "date", "required": true}, "dob": {"title": "Date Of Birth", "type": "date", "placeholder": "Enter Date Of Birth"}, "gender": {"title": "Gender", "type": "radio", "enum": [{"const": "M", "title": "Male"}, {"const": "F", "title": "Female"}], "placeholder": "Select Gender"}, "member_type": {"title": "Member Type", "type": "select", "enum": [{"const": "primary", "title": "Primary"}, {"const": "regular", "title": "Regular"}, {"const": "associate", "title": "Associate"}, {"const": "tenant", "title": "Tenant"}], "default": "primary", "disabled": true}}}, "unit_deatils": {"title": "Unit Details", "layout": "group", "children": {"building_name": {"title": "Building Name", "type": "string", "required": true, "placeholder": "Enter Building Name"}, "floor_no": {"title": "Floor No", "type": "string", "required": true, "placeholder": "Enter Floor No"}, "unit": {"title": "Unit", "type": "string", "required": true, "placeholder": "Enter Unit"}, "unit_category": {"title": "Unit Category", "type": "string", "required": true, "placeholder": "Enter Unit Category"}, "Remove previous primary member?": {"title": "Remove previous primary member?", "type": "radio", "enum": [{"title": "Yes", "const": "Yes"}, {"title": "No", "const": "No"}], "default": "No"}}}, "member_name": {"title": "Select Associate / Nominee", "type": "dropdown", "required": true, "placeholder": "Enter Associate / Nominee", "apiPath": "admin/member/unitTransfer/[id]", "labelKeys": ["member_name"]}, "floor_no": {"title": "Member type of previous primary member", "type": "string", "placeholder": "Enter Member type of previous primary member", "default": "associate", "disabled": true}}}}}