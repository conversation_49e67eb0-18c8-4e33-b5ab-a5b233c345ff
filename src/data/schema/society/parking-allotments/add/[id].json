{"meta": {"title": "New / Edit Allocate Parking", "data_source": "parking-allotments/add/:id"}, "schema": {"rules": [], "actions": [{"title": "Save", "type": "submit", "api_path": "parking-allotments/add", "redirect": "/admin/society/parking-allotments/list"}, {"title": "Save & New", "type": "submit", "api_path": "parking-allotments/add", "redirect": "/admin/society/parking-allotments/add"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/parking-allotments/list"}], "flow": {"children": {"fk_unit_id": {"title": "Allot parking to", "type": "dropdown", "required": true, "placeholder": "Select a unit", "apiPath": "v2/admin/units/list", "labelKeys": ["building_unit"], "onMount": true}, "fk_parking_unit_id": {"title": "Parking unit", "type": "dropdown", "required": true, "placeholder": "Select a parking unit", "apiPath": "/admin/parkingUnits/list", "labelKeys": ["parking_number"]}, "parking_number": {"title": "Parking Number", "type": "string", "placeholder": "Enter parking number"}, "parking_type": {"title": "Parking Type", "type": "string", "required": true, "placeholder": "Enter Parking Type"}, "allotment_for": {"title": "Space available for", "type": "select", "required": true, "enum": [{"const": "2wheeler", "title": "<PERSON>"}, {"const": "4wheeler", "title": "<PERSON>"}], "placeholder": "Select an option"}, "effective_date": {"title": "Allotment w.e.f.", "type": "date", "required": true, "placeholder": "dd/mm/yyyy"}, "allowed_number_of_parkings": {"title": "Maximum vehicle(s) allowed", "type": "select", "enum": [{"const": 0, "title": "Select an option"}, {"const": 1, "title": "1"}, {"const": 2, "title": "2"}, {"const": 3, "title": "3"}, {"const": 4, "title": "4"}, {"const": 5, "title": "5"}]}}}}}