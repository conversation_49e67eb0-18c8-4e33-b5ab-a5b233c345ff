{"meta": {"title": "New Vehicles Registration"}, "schema": {"facts": [{"fact": "flatList", "path": "/admin/units/list?building_id=[id]&soc_building_floor=[soc_building_floor]", "method": "GET", "dependsOn": "soc_building_name", "xpath": "id", "params": {"soc_building_name": {"fact": "soc_building_name", "path": "id"}}, "allowOn": {"soc_building_floor": {"fact": "soc_building_floor", "operator": "isNumber"}}}], "rules": [{"conditions": {"any": [{"fact": "unit_number", "path": "id", "operator": "falsy"}]}, "event": [{"type": "require", "params": {"field": ["soc_building_name", "soc_building_floor", "flat"]}}]}, {"conditions": {"any": [{"fact": "soc_building_name", "path": "id", "operator": "falsy"}, {"fact": "soc_building_floor", "operator": "falsy"}, {"fact": "flat", "path": "id", "operator": "falsy"}]}, "event": [{"type": "require", "params": {"field": ["unit_number"]}}]}, {"conditions": {"any": [{"fact": "soc_building_name", "path": "id", "operator": "truthy"}]}, "event": [{"type": "uiSchemaReplace", "params": {"soc_building_floor.ui:disabled": false}}]}, {"conditions": {"any": [{"fact": "soc_building_floor", "operator": "isNumber"}]}, "event": [{"type": "uiSchemaReplace", "params": {"flat.ui:disabled": false}}]}, {"conditions": {"any": [{"fact": "unit_number", "path": "id", "operator": "notEqual", "value": {"fact": "unit_temp"}}]}, "event": [{"type": "update", "params": {"soc_building_name": {"fact": "unit_number", "path": "soc_building_id"}, "unit_temp": {"fact": "unit_number", "path": "id"}}}]}, {"conditions": {"all": [{"fact": "soc_building_name", "path": "floor_array", "operator": "isArray"}]}, "event": [{"type": "schemaOverride", "params": {"soc_building_floor.enum": {"fact": "soc_building_name", "path": "floor_array"}}}]}, {"conditions": {"all": [{"fact": "soc_building_name", "path": "id", "operator": "truthy"}, {"fact": "soc_building_floor", "operator": "isNumber"}, {"fact": "flatList", "operator": "isArray"}]}, "event": {"type": "uiSchemaReplace", "params": {"flat.ui:options.loadedOptions": {"fact": "flatList"}}}}, {"conditions": {"any": [{"fact": "soc_building_name", "path": "id", "operator": "truthy"}, {"fact": "soc_building_floor", "operator": "isNumber"}, {"fact": "flat", "path": "id", "operator": "truthy"}]}, "event": [{"type": "update", "params": {"unit_number": {"fact": "flat", "path": "id"}}}]}], "actions": [{"title": "Save", "type": "submit", "api_path": "parking/registervehicle", "redirect": "/admin/society/parking/list"}, {"title": "Reset", "type": "reset"}, {"title": "Cancel", "type": "cancel", "redirect": "/admin/society/parking/list"}], "flow": {"children": {"unit_number": {"title": "Unit Number Bilal", "type": "dropdown", "apiPath": "/v2/admin/units/list", "labelKeys": ["building_unit"], "placeholder": "Select Unit Number", "dependent": [{"field": "soc_building_id"}]}, "unit_temp": {"type": "number", "hidden": true}, "or_divider": {"title": "OR", "type": "null", "hideLabel": false}, "soc_building_name": {"title": "Building(s)", "type": "dropdown", "apiPath": "/admin/building/list", "labelKeys": ["soc_building_name"], "placeholder": "Select Building", "onMount": true, "dependent": [{"field": "floor_array"}]}, "soc_building_floor": {"title": "Floor(s)", "type": "select", "placeholder": "Select Floor", "enum": [], "minItems": 0, "disabled": true}, "flat": {"title": "Flat(s)", "type": "dropdown", "labelKeys": ["unit_flat_number"], "placeholder": "Select Units", "disabled": true}}}}}