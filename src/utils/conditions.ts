/* =========================================================================
   UNIVERSAL CONDITION EVALUATOR ─ SINGLE PUBLIC FUNCTION
   ========================================================================= */

/* ---------- 1. Public types ------------------------------------------------ */

export type ComparisonOperator =
  | 'equal' | 'notEqual'
  | 'greaterThan' | 'lessThan'
  | 'greaterThanOrEqual' | 'lessThanOrEqual'
  | 'contains' | 'in';

export type LogicOperator = 'any' | 'all';

/**
 * A single field comparison.
 */
export interface Condition {

  /** Row property to read.  `null` ⇒ compare the entire row object (rare). */
  key: string | null;
  operator: ComparisonOperator;
  value: unknown;
}

/**
 * Logical group of sub-conditions (can nest indefinitely).
 */
export interface LogicalCondition {

  /** Defaults to `'any'` (OR) if omitted. */
  logic?: LogicOperator;
  conditions: (Condition | LogicalCondition)[];
}

/* ---------- 2. Public API -------------------------------------------------- */

/**
 * Evaluate **any** back-end condition payload against a row object.
 *
 * Input formats supported out-of-the-box
 * ──────────────────────────────────────
 *  1. Logical block (recommended)  
 *     `{ logic:'all'|'any', conditions:[ … ] }`
 *
 *  2. Flat keyed map of values / arrays / operator objects  
 *     `{ status: 1, type:['A','B'], age:{operator:'greaterThan',value:65} }`
 *
 *  3. Legacy wrappers  
 *     `{ disable:[1] }`, `{ hide:{ conditions:[…] } }`, `{ show:1 }`
 *
 *  4. Single leaf condition object  
 *     `{ key:'status', operator:'equal', value:1 }`
 *
 *  5. Root-level array (OR of elements)  
 *     `[ {key:'status',operator:'equal',value:1}, … ]`
 *
 *  6. Root-level primitive  
 *     `'admin'`, `42`, `null`  ➜ compared against the entire row object.
 *
 * @param row      A single table row (plain object).
 * @param rawInput Condition payload from the back-end (any of the formats above).
 *
 * @returns `true` if the row satisfies the condition payload, otherwise `false`.
 *
 * @example
 * // ALL logic – both clauses must pass
 * const row = { user_id:null, status:1 };
 * const cond = {
 *   logic:'all',
 *   conditions:[
 *     { key:'user_id', operator:'equal', value:null },
 *     { key:'status',  operator:'equal', value:1 }
 *   ]
 * };
 * const ok = evaluateConditions(row, cond);   // ➜ true
 *
 * @example
 * // Flat keyed map with arrays and operators
 * const row = { member_type_name:'Tenant', status:3 };
 * const cond = {
 *   member_type_name:'Tenant',            // primitive equality
 *   status:[1,2,3],                       // array inclusion (OR)
 *   age:{ operator:'greaterThan', value:18 }
 * };
 * evaluateConditions(row, cond);          // ➜ true
 *
 * @example
 * // Legacy disable wrapper
 * const row = { disable:2 };
 * evaluateConditions(row, { disable:[1,2] });       // ➜ true
 */
export function evaluateConditions(
  row: Record<string, unknown>,
  rawInput: unknown,
): boolean {
  const normalised = normalise(rawInput);

  return evalNode(row, normalised);
}

/* ---------- 3. Normalisation ---------------------------------------------- */

type Normalised = Condition | LogicalCondition;

/** Converts any supported input into a LogicalCondition/Condition tree. */
function normalise(input: unknown): Normalised {
  /* Leaf condition already?  { key, operator, value } */
  if (
    input !== null &&
    typeof input === 'object' &&
    'key' in input &&
    'operator' in input &&
    'value' in input
  ) {
    return input as Condition;
  }

  /* Logical block already? */
  if (
    input !== null &&
    typeof input === 'object' &&
    'conditions' in input &&
    Array.isArray((input as any).conditions)
  ) {
    const lg = input as LogicalCondition;

    return {
      logic: lg.logic ?? 'any',
      conditions: lg.conditions.map(normalise),
    };
  }

  /* Flat keyed map */
  if (input !== null && typeof input === 'object' && !Array.isArray(input)) {
    const obj = input as Record<string, unknown>;
    const list: (Condition | LogicalCondition)[] = [];

    for (const [key, val] of Object.entries(obj)) {
      if (key === 'disable' || key === 'hide' || key === 'show') {
        list.push(...legacyToConditions(key, val));
        continue;
      }

      if (Array.isArray(val)) {
        val.forEach(v => list.push({ key, operator:'equal', value:v }));
      } else if (
        val !== null &&
        typeof val === 'object' &&
        'operator' in val &&
        'value' in val
      ) {
        const { operator, value } = val as { operator: ComparisonOperator; value: unknown };
        
        list.push({ key, operator, value });
      } else {
        list.push({ key, operator:'equal', value:val });
      }
    }

    return { logic:'any', conditions:list };
  }

  /* Root-level array */
  if (Array.isArray(input)) {
    return { logic:'any', conditions: input.map(normalise) };
  }

  /* Primitive or anything else ⇒ compare with entire row */
  return { logic:'any', conditions:[{ key:null, operator:'equal', value:input }] };
}

/** Expand legacy wrappers into proper conditions. */
function legacyToConditions(
  key: string,
  val: unknown,
): (Condition | LogicalCondition)[] {
  if (Array.isArray(val)) {
    return val.map(v => ({ key, operator:'equal', value:v }));
  }

  if (
    val !== null &&
    typeof val === 'object' &&
    'conditions' in val
  ) {
    return [normalise(val)];
  }

  return [{ key, operator:'equal', value:val }];
}

/* ---------- 4. Recursive evaluation --------------------------------------- */

function evalNode(row: Record<string, unknown>, node: Normalised): boolean {
  if ('conditions' in node) {
    const logic = (node.logic ?? 'any') as LogicOperator;
    const results = node.conditions.map(c => evalNode(row, c));

    return logic === 'all' ? results.every(Boolean) : results.some(Boolean);
  }

  const actual = node.key ? row[node.key] : row;          // leaf

  return evalLeaf(actual, node);
}

export function evalLeaf(actual: unknown, cond: Condition): boolean {
  const { operator, value: expected } = cond;

  switch (operator) {
    case 'equal':
      return expected === null
        ? actual === null || actual === undefined
        : actual === expected;

    case 'notEqual':
      return expected === null
        ? actual !== null && actual !== undefined
        : actual !== expected;

    case 'greaterThan':
      return isNum(actual) && isNum(expected) && actual >  expected;
    case 'lessThan':
      return isNum(actual) && isNum(expected) && actual <  expected;
    case 'greaterThanOrEqual':
      return isNum(actual) && isNum(expected) && actual >= expected;
    case 'lessThanOrEqual':
      return isNum(actual) && isNum(expected) && actual <= expected;

    case 'contains':
      return typeof actual === 'string' &&
             typeof expected === 'string' &&
             actual.includes(expected);

    case 'in':
      return Array.isArray(expected) && expected.includes(actual);

    default:
      return false;
  }
}

const isNum = (v: unknown): v is number =>
  typeof v === 'number' && Number.isFinite(v);
