/**
 * Retrieves a nested value from an object based on a given path.
 * Supports object properties, array indices, and a wildcard `"*"` to
 * collect values from every element in an array.
 *
 * @param obj - The target object to read from. Must be a non-null object.
 * @param path - Either a dot-delimited string (e.g. "user.profile.name")
 *               or an array of path segments (e.g. ["user","profile","name"]).
 *               Use "*" in the path to mean “every element of this array.”
 * @returns The value found at that path, or `undefined` if any step is missing.
 *          If the path contains `"*"`, returns an array of results.
 */
export function getNestedValue(obj: any, path: string | string[]): any {
  // Basic validation
  if (obj == null || typeof obj !== "object") {
    throw new Error("Invalid target object provided. It must be a non-null object.");
  }

  // Normalize path to array form
  const segments = typeof path === "string"
    ? (path === "" ? [] : path.split("."))
    : path;

  if (!Array.isArray(segments) || segments.length === 0) {
    // no segments => just return the object itself
    return obj;
  }

  // Handle wildcard
  const wcIdx = segments.indexOf("*");

  if (wcIdx !== -1) {
    const pre = segments.slice(0, wcIdx);
    const post = segments.slice(wcIdx + 1);

    // Traverse up to the array
    let cursor: any = obj;

    for (const seg of pre) {
      const key = isNaN(+seg) ? seg : +seg;

      if (cursor == null) return undefined;
      cursor = cursor[key];
    }

    if (!Array.isArray(cursor)) {
      throw new Error(`Expected an array at path "${pre.join(".")}"`);
    }

    // Map each element through the remainder of the path
    return cursor.map((item: any) => getNestedValue(item, post));
  }

  // No wildcard: simple linear descent
  let result: any = obj;

  for (const seg of segments) {
    const key = isNaN(+seg) ? seg : +seg;

    if (result == null) {
      return undefined;
    }

    result = result[key];
  }

  return result;
}

/**
 * Updates a nested value inside an object based on a given path.
 * It supports both object properties and array indices. Additionally,
 * it supports using a wildcard `"*"` in the path to update a property
 * for every object in an array.
 *
 * When the path includes a `"*"`, it indicates that at that level the property is an array,
 * and the provided `newValue` should also be an array. The function will update the nested
 * property (specified in the remaining path) for each object in that array with the corresponding
 * value from `newValue`.
 *
 * @param {object} obj - The target object to update. Must be a non-null object.
 * @param {string[]} path - An array representing the path to the property.
 *    If the path includes a `"*"`, the corresponding property is expected to be an array.
 *    The path must not be empty.
 * @param {any} newValue - The new value to set at the target path.
 *    When using a wildcard, this should be an array of values, one for each element in the target array.
 *    If `newValue` is `undefined`, the property will be deleted.
 *
 * @throws {Error} If `obj` is not a valid object, the path is empty, or if an expected array or object is missing in the path.
 *
 * @example
 * // Example 1: Updating a nested object field
 * const data1 = { user: { profile: { name: "Alice" } } };
 * updateNestedValue(data1, ["user", "profile", "name"], "Bob");
 * console.log(data1); // { user: { profile: { name: "Bob" } } }
 *
 * @example
 * // Example 2: Updating an array element
 * const data2 = { items: [{ value: 10 }] };
 * updateNestedValue(data2, ["items", "0", "value"], 20);
 * console.log(data2); // { items: [{ value: 20 }] }
 *
 * @example
 * // Example 3: Using wildcard "*" in the path to update all elements of an array
 * const data3 = { particulars: [
 *   { particular_total: 5 },
 *   { particular_total: 7 },
 *   { particular_total: 9 }
 * ] };
 * updateNestedValue(data3, ["particulars", "*", "particular_total"], [10, 20, 30]);
 * console.log(data3);
 * // {
 * //   particulars: [
 * //     { particular_total: 10 },
 * //     { particular_total: 20 },
 * //     { particular_total: 30 }
 * //   ]
 * // }
 */
export function updateNestedValue(obj: any, path: string[], newValue: any): void {
  try {
    if (!path || path.length === 0) {
      throw new Error("The path must be a non-empty array of strings.");
    }

    // Validate target object.
    if (obj === null || typeof obj !== "object") {
      throw new Error("Invalid target object provided. It must be a non-null object.");
    }

    // Validate that path is a non-empty array.
    if (!Array.isArray(path) || path.length === 0) {
      throw new Error("The path must be a non-empty array of strings.");
    }

    // Check for wildcard usage
    const wildcardIndex = path.indexOf("*");

    if (wildcardIndex !== -1) {
      if (!Array.isArray(newValue)) {
        throw new Error(`Expected newValue to be an array when using wildcard "*" in path.`);
      }

      const prePath = path.slice(0, wildcardIndex);
      const postPath = path.slice(wildcardIndex + 1);

      let currentLevel: any = obj;

      for (const key of prePath) {
        const isArrayIdx = !isNaN(Number(key));
        const parsed = isArrayIdx ? Number(key) : key;

        if (currentLevel[parsed] === undefined) {
          currentLevel[parsed] = isArrayIdx ? [] : {};
        }

        currentLevel = currentLevel[parsed];
      }

      if (!Array.isArray(currentLevel)) {
        throw new Error(`Expected an array at ${prePath.join('.')}`);
      }

      for (let i = 0; i < currentLevel.length; i++) {
        if (i >= newValue.length) continue;
        updateNestedValue(currentLevel[i], postPath, newValue[i]);
      }

      return;
    }

    // Non-wildcard traversal
    let currentLevel: any = obj;

    for (let i = 0; i < path.length - 1; i++) {
      const key = path[i];
      const isArrayIdx = !isNaN(Number(key));
      const parsed: string | number = isArrayIdx ? Number(key) : key;

      if (isArrayIdx) {
        if (!Array.isArray(currentLevel)) {
          let errorPath = path.slice(0, i + 1).join('.');

          if (errorPath.endsWith('.0')) {
            errorPath = errorPath.replace(/\.0$/, '');
          }

          throw new Error(`Expected an array at ${errorPath}`);
        }

        if (currentLevel[parsed] === undefined) {
          currentLevel[parsed] = {};
        }
      } else {
        if (currentLevel[parsed] === undefined) {
          currentLevel[parsed] = {};
        }
      }

      currentLevel = currentLevel[parsed];
    }

    const lastKey = path[path.length - 1];
    const isLastArrayIdx = !isNaN(Number(lastKey));
    const parsedLast: string | number = isLastArrayIdx ? Number(lastKey) : lastKey;

    if (newValue === undefined) {
      delete currentLevel[parsedLast];
    } else {
      currentLevel[parsedLast] = newValue;
    }
  } catch (err) {
    console.warn(`Failed to update nested value at path "${path?.join('.')}"`, err);
  }
}

/**
 * Recursively searches an object or array for a nested property that meets the following criteria:
 * - The object has a `fact` property that is one of the specified `factNames`.
 * - If such an object also has a `params.affects` property (which is either a string or an array of strings),
 *   that value is returned; otherwise, the `fact` value is returned.
 *
 * If no object meeting these criteria is found, the function returns null.
 *
 * @param obj - The object or array to search.
 * @param factNames - An array of fact names to look for.
 * @returns The matching `params.affects` value (if present and valid), or the `fact` value, or null.
 *
 * @example
 * // Example with `params.affects` as a string:
 * const obj1 = {
 *   fact: 'purchaseformlist',
 *   params: { affects: 'someValue' }
 * };
 * // Returns "someValue" because the fact is in factNames and params.affects exists.
 * console.log(findMatchingFact(obj1, ['purchaseformlist'])); // "someValue"
 *
 * @example
 * // Example without `params.affects`:
 * const obj2 = {
 *   fact: 'purchaseformlist'
 * };
 * // Returns "purchaseformlist" because no affects value is available.
 * console.log(findMatchingFact(obj2, ['purchaseformlist'])); // "purchaseformlist"
 *
 * @example
 * // Example with nested objects:
 * const obj3 = {
 *   event: {
 *     params: {
 *       details: {
 *         fact: 'vendor_id',
 *         params: { affects: ['affect1', 'affect2'] }
 *       }
 *     }
 *   }
 * };
 * // Returns ["affect1", "affect2"] because vendor_id is in factNames.
 * console.log(findMatchingFact(obj3, ['vendor_id'])); // ["affect1", "affect2"]
 */
export function findMatchingFact(
  obj: unknown,
  factNames: string[]
): string | string[] | null {
  if (Array.isArray(obj)) {
    for (const item of obj) {
      const found = findMatchingFact(item, factNames);

      if (found !== null) {
        return found;
      }
    }
  } else if (obj !== null && typeof obj === 'object') {
    const record = obj as Record<string, any>;


    // Check if this object has a `fact` property that is in factNames.
    if (typeof record.fact === 'string' && factNames.includes(record.fact)) {

      // If a valid `params.affects` exists, return that.
      if (record.params && typeof record.params === 'object') {
        const affects = record.params.affects;

        if (
          typeof affects === 'string' ||
          (Array.isArray(affects) && affects.every((item) => typeof item === 'string'))
        ) {
          return affects;
        }
      }


      // Otherwise, return the fact value.
      return record.fact;
    }


    // Otherwise, continue searching in all properties.
    for (const key in record) {
      if (Object.prototype.hasOwnProperty.call(record, key)) {
        const found = findMatchingFact(record[key], factNames);

        if (found !== null) {
          return found;
        }
      }
    }
  }


  return null;
}

/**
 * Recursively transforms raw formData into a flattened payload suitable for API consumption.
 * - Converts nested objects with `id` properties into their IDs.
 * - Flattens objects when uiSchema defines a layout.
 * - Converts arrays of objects into arrays of simplified objects (extracting `id` where available).
 * - Preserves primitive values and nulls.
 * - Avoids injecting schema defaults — uses only what the user has input.
 * - Applies widget/field mapping logic from RJSF conventions (e.g., `typehead`, `TableWidget`).
 * - Coerces primitive values based on declared ui:options.type.
 * - Logs warnings for unexpected types or unknown widgets.
 *
 * @param formData - The original form state data.
 * @param uiSchema - UI Schema definition from RJSF (optional).
 * @returns A new object containing only primitive values or IDs, structured for backend APIs.
 */
export function processFormData(
  formData: Record<string, any>,
  uiSchema: Record<string, any> = {}
): Record<string, any> {
  const result: Record<string, any> = {};

  function isTypeheadField(uiField: string | undefined): boolean {
    return uiField === 'typehead';
  }

  function isCustomWidget(widget: string | undefined): boolean {
    return [
      'TableWidget',
      'ImageWidget',
      'TimeWidget',
      'CameraWidget',
    ].includes(widget ?? '');
  }

  function coerceValueByType(value: any, type?: string): any {
    if (value === null) return null;

    if (type === 'boolean') {
      if (value === true || value === 'true') return true;
      if (value === false || value === 'false') return false;
    }

    if (type === 'number' || type === 'integer') {
      const parsed = parseFloat(value);

      return isNaN(parsed) ? value : parsed;
    }

    if (type === 'string') {
      return String(value);
    }

    return value;
  }

  function traverse(
    data: any,
    uiSchemaNode: Record<string, any>,
    parent: Record<string, any>
  ) {
    if (data === null) return;
    if (typeof data !== 'object') return;

    Object.entries(data).forEach(([key, value]) => {
      const uiNode = uiSchemaNode?.[key] || {};
      const uiOptions = uiNode['ui:options'] || {};
      const uiField = uiNode['ui:field'];
      const uiWidget = uiNode['ui:widget'];
      const fieldType = uiOptions?.type;

      try {
        if (value === null) {
          parent[key] = null;

          return;
        }

        // Flatten layout containers (e.g., group rows/columns)
        if ((uiOptions?.layout || uiField === 'LayoutGridField') && typeof value === 'object' && !Array.isArray(value)) {
          traverse(value, uiNode, parent);

          return;
        }

        // Handle typehead (dropdown/autocomplete)
        if (isTypeheadField(uiField)) {
          // Single-value branch
          if (!Array.isArray(value) && value !== null && typeof value === 'object') {
            // empty object → null
            if (isEmptyObject(value)) {
              parent[key] = null;
            }

            // object-with-id → its id
            else if ('id' in value) {
              parent[key] = (value as any).id;
            }

            // some other object → keep as-is
            else {
              parent[key] = value;
            }
          }

          // Array branch: leave empty objects in the array, but still unwrap ids
          else if (Array.isArray(value)) {
            parent[key] = value.map((v: any) => {
              if (v !== null && typeof v === 'object' && 'id' in v) {
                return (v as any).id;
              }

              return v;
            });
          }

          // Everything else (string, number, etc.)
          else {
            parent[key] = value;
          }

          return;
        }

        // Handle file/image/table/camera widgets
        if (isCustomWidget(uiWidget)) {
          if (uiWidget === 'TableWidget') {
            if (uiOptions?.editable) {
              parent[key] = Array.isArray(value) ? value : [];
            } else {
              parent[key] = (Array.isArray(value) ? value : [])?.filter((v: any) => v?.id && v?.selected).map((v: any) => v?.id);
            }
          } else {
            parent[key] = value;
          }

          return;
        }

        // Unknown widget warning
        if (uiWidget && !isCustomWidget(uiWidget)) {
          console.warn(`Unknown widget for key "${key}":`, uiWidget);
        }

        // Handle arrays
        if (Array.isArray(value)) {
          parent[key] = value.map(item => {
            if (item && typeof item === 'object') {
              if ('id' in item) return item.id;
              const nested: Record<string, any> = {};

              traverse(item, uiNode?.items || {}, nested);

              return nested;
            }

            return coerceValueByType(item, fieldType);
          });

          return;
        }

        // Handle nested objects
        if (value && typeof value === 'object') {
          if (isEmptyObject(value)) {
            parent[key] = null;
          } else if ('id' in value) {
            parent[key] = value.id;
          } else {
            parent[key] = {};
            traverse(value, uiNode, parent[key]);
          }

          return;
        }

        const coerceValue = coerceValueByType(value, fieldType);

        console.log(`coerced value for ${key} is ${coerceValue} with type of ${typeof coerceValue}`)

        // Primitive value with type-based coercion
        parent[key] = coerceValue;
      } catch (err) {
        console.warn(`Failed to process key: ${key}`, err);
        parent[key] = value;
      }
    });
  }

  traverse(formData, uiSchema, result);

  return result;
}

/**
 * Checks if an object is empty (has no own properties).
 * 
 * @param obj - The object to check.
 * @returns True if the object is empty and is a plain object, false otherwise.
 */
export const isEmptyObject = (obj: any): boolean => {
  return Object.keys(obj).length === 0 && obj.constructor === Object;
};

/**
 * Checks if a value is a non-null object (not an array).
 * 
 * @param value - The value to check.
 * @returns True if the value is a non-null object, false otherwise.
 */
export const isObject = (value: any): boolean => {
  return typeof value === 'object' &&
    value !== null &&
    value.constructor === Object;
};

/**
 * @title Merge Default Parameters with Form Data
 *
 * @description
 * Merges an array of default parameters and mappings with a form data object.
 * Parameters can be:
 *   - strings: looks up the value by key in formData, or uses the key as value if not present.
 *   - mapping objects: maps output key to the value of a formData key, or to a fallback value if formData key is missing.
 *
 * @template T The shape of the form data.
 * @template R The resulting merged object type.
 *
 * @param params - An array of parameter keys (as strings) or explicit mappings of outputKey to formDataKey or to fixed values.
 *   Example: ['type', { kumail: 'grace_period' }, { status: 'active' }]
 * @param formData - The form data object to pull values from.
 *
 * @returns An object containing key-value pairs merged as per the rules described above.
 *
 * @example
 * ```
 * // Example 1: String keys, use values from formData
 * mergeDefaultParams(['foo', 'bar'], { foo: 'one', bar: 'two' });
 * // returns: { foo: 'one', bar: 'two' }
 *
 * // Example 2: String key missing from formData
 * mergeDefaultParams(['foo', 'missing'], { foo: 'exists' });
 * // returns: { foo: 'exists', missing: 'missing' }
 *
 * // Example 3: Mapping another key
 * mergeDefaultParams([{ baz: 'foo' }], { foo: 100 });
 * // returns: { baz: 100 }
 *
 * // Example 4: Mapping to a fixed value, ignoring formData
 * mergeDefaultParams([{ type: 'maintenance' }], { type: 'somethingElse' });
 * // returns: { type: 'maintenance' }
 *
 * // Example 5: Mixed parameters (string keys and mapping objects)
 * mergeDefaultParams(['alpha', { beta: 'gamma' }, { status: 'active' }], { alpha: 42, gamma: 99 });
 * // returns: { alpha: 42, beta: 99, status: 'active' }
 * ```
 */
export function mergeDefaultParams(
  params: Array<string | Record<string, string>>,
  formData: Record<string, any>
): Record<string, any> {
  return params.reduce((acc: any, p) => {
    if (typeof p === 'string') {
      acc[p] = formData[p] !== undefined ? formData[p] : p;
    } else if (typeof p === 'object' && p !== null) {
      const [outputKey, valueOrFormKey] = Object.entries(p)[0];

      acc[outputKey] = formData[valueOrFormKey] !== undefined ? formData[valueOrFormKey] : valueOrFormKey;
    }

    return acc;
  }, {});
}