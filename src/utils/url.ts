/**
 * Supported file extensions in the application
 */
type SupportedFileExtension = 'pdf' | 'xlsx' | 'xls' | 'csv';

/**
 * MIME type mapping for supported file extensions
 */
type MimeTypeMap = Record<SupportedFileExtension, string>;

/**
 * Configuration for file type determination
 */
interface FileTypeConfig {
  supportedExtensions: SupportedFileExtension[];
  defaultExtension: SupportedFileExtension;
}


/**
 * Extracts file extension from a given URL
 * 
 * @param url - The URL containing the filename with extension
 * @returns The lowercase file extension without the dot
 * 
 * @example
 * ```
 * const extension = getFileExtensionFromUrl('https://storage.amazonaws.com/path/document.pdf');
 * console.log(extension); // 'pdf'
 * ```
 * 
 * @example
 * ```
 * const extension = getFileExtensionFromUrl('https://api.example.com/files/report.xlsx');
 * console.log(extension); // 'xlsx'
 * ```
 * 
 * @throws {Error} When URL is invalid or doesn't contain a filename with extension
 */
function getFileExtensionFromUrl(url: string): string {
  if (!url || typeof url !== 'string') {
    throw new Error('Invalid URL provided');
  }
  
  const fileName = url.split('/').pop();

  if (!fileName) {
    throw new Error('No filename found in URL');
  }
  
  const parts = fileName.split('.');

  if (parts.length < 2) {
    throw new Error('No extension found in filename');
  }
  
  return parts.pop()!.toLowerCase();
}

/**
 * Extracts file type from API endpoint URL
 * 
 * @param apiUrl - The API endpoint URL containing file type information
 * @returns The file type extracted from the URL path
 * 
 * @example
 * ```
 * const fileType = getFileTypeFromEndpoint('/admin/income-details/download/pdf');
 * console.log(fileType); // 'pdf'
 * ```
 * 
 * @example
 * ```
 * const fileType = getFileTypeFromEndpoint('/api/reports/export/xlsx');
 * console.log(fileType); // 'xlsx'
 * ```
 * 
 * @throws {Error} When API URL is invalid or doesn't contain file type information
 */
function getFileTypeFromEndpoint(apiUrl: string): string {
  if (!apiUrl || typeof apiUrl !== 'string') {
    throw new Error('Invalid API URL provided');
  }
  
  const urlParts = apiUrl.split('/').filter(part => part.length > 0);

  if (urlParts.length === 0) {
    throw new Error('No path segments found in API URL');
  }
  
  const lastPart = urlParts[urlParts.length - 1];

  return lastPart.toLowerCase();
}

/**
 * Maps file extension to corresponding MIME type
 * 
 * @param extension - The file extension (with or without dot)
 * @returns The corresponding MIME type string
 * 
 * @example
 * ```
 * const mimeType = getMimeTypeFromExtension('pdf');
 * console.log(mimeType); // 'application/pdf'
 * ```
 * 
 * @example
 * ```
 * const mimeType = getMimeTypeFromExtension('.xlsx');
 * console.log(mimeType); // 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
 * ```
 * 
 * @example
 * ```
 * const mimeType = getMimeTypeFromExtension('unknown');
 * console.log(mimeType); // 'application/octet-stream'
 * ```
 */
export function getMimeTypeFromExtension(extension: string): string {
  const mimeTypes: MimeTypeMap = {
    'pdf': 'application/pdf',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xls': 'application/vnd.ms-excel',
    'csv': 'text/csv'
  };
  
  // Remove leading dot if present
  const cleanExtension = extension.startsWith('.') ? extension.substring(1) : extension;
  const normalizedExtension = cleanExtension.toLowerCase() as SupportedFileExtension;
  
  return mimeTypes[normalizedExtension] || 'application/octet-stream';
}

/**
 * Determines file type from S3 URL or API endpoint with fallback strategy
 * 
 * @param s3Url - The S3 URL containing the actual filename
 * @param apiUrl - The API endpoint URL as fallback
 * @param config - Optional configuration for supported extensions and defaults
 * @returns The determined file extension
 * 
 * @example
 * ```
 * const fileType = determineFileType(
 *   'https://storage.amazonaws.com/bucket/document.pdf',
 *   '/admin/download/pdf'
 * );
 * console.log(fileType); // 'pdf'
 * ```
 * 
 * @example
 * ```
 * const fileType = determineFileType(
 *   'invalid-url',
 *   '/admin/export/xlsx',
 *   { supportedExtensions: ['pdf', 'xlsx', 'csv'], defaultExtension: 'pdf' }
 * );
 * console.log(fileType); // 'xlsx'
 * ```
 * 
 * @example
 * ```
 * // With custom configuration
 * const config: FileTypeConfig = {
 *   supportedExtensions: ['pdf', 'csv'],
 *   defaultExtension: 'csv'
 * };
 * const fileType = determineFileType('invalid-url', 'invalid-endpoint', config);
 * console.log(fileType); // 'csv'
 * ```
 */
export function determineFileType(
  s3Url: string, 
  apiUrl: string, 
  config: FileTypeConfig = {
    supportedExtensions: ['pdf', 'xlsx', 'xls', 'csv'],
    defaultExtension: 'pdf'
  }
): SupportedFileExtension {
  const { supportedExtensions, defaultExtension } = config;
  
  // Try S3 URL first (more reliable)
  try {
    const extension = getFileExtensionFromUrl(s3Url);

    if (supportedExtensions.includes(extension as SupportedFileExtension)) {
      return extension as SupportedFileExtension;
    }
  } catch (error) {
    console.warn('Could not extract extension from S3 URL:', error);
  }
  
  // Fallback to API endpoint
  try {
    const fileType = getFileTypeFromEndpoint(apiUrl);

    if (supportedExtensions.includes(fileType as SupportedFileExtension)) {
      return fileType as SupportedFileExtension;
    }
  } catch (error) {
    console.warn('Could not extract file type from API URL:', error);
  }
  
  // Default fallback
  console.warn(`Using default file type: ${defaultExtension}`);

  return defaultExtension;
}
