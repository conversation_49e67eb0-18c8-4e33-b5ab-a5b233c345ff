import TableScreen from './screens/TableScreen'
import DashboardScreen from './screens/DashboardScreen'
import MixScreen from './screens/MixScreen'

import SocProfileScreen from './screens/SocProfileScreen'
import type { MenuItemData } from '@/types/menuTypes'

// import { getSchema } from "@/app/api/schema/route";
import FormBuilder from '@/views/form/builder/server'
import HtmlRenderer from '@/components/HtmlParse'
import BulkUnitScreen from './screens/BulkUnitScreen'
import NewBulkUnitScreen from './screens/NewBulkUnitScreen'
import PreviewRuleCard from '@/components/card/PreviewRuleCard'
import MyForm from '@/components/customForm/DemoForm'
import EditNewRule from '@/components/customForm/EditNewRule'
import PDFViewer from './screens/PDFScreen'
import { extractParamsFromUrl, stripAdminScopedPath } from '@/utils/string'
import { replacePlaceholders } from '@/utils/replacePlaceholders'
import NocFormsPage from '@/components/nocForms/NocFormsPage'
import ComplaintDetailsPage from '@/components/helpdesk/ComplaintDetailsPage'
import ShareCertificateWrapper from '@/components/shareCertficate/ShareCertificateWrapper'
import AddMemberShares from '@/components/shareCertficate/AddMemberShares'

type PageTypeScreenProps = {
  pageData: MenuItemData
  pathname: string
  sectionName?: string
}

const PageTypeScreen = async ({ pageData, pathname, sectionName }: PageTypeScreenProps) => {
  const { pageType, href, order, commonPageConfig, apiURL } = pageData
  const param = extractParamsFromUrl(pathname, href)
  const customURL = stripAdminScopedPath(replacePlaceholders(apiURL || href, param) || pathname)

  switch (pageType) {
    case 'htmlContent': {
      return <HtmlRenderer apiURL={customURL} />
    }

    case 'table':
      return <TableScreen sectionName={sectionName} shouldAllowBack={true} />
    case 'dashboard':
      return <DashboardScreen />
    case 'mix':
      return (
        <MixScreen
          params={param}
          pathname={customURL}
          order={order || []}
          commonPageConfig={commonPageConfig}
          href={href}
        />
      )
    case 'PDF':
      return <PDFViewer customUrl={customURL} />
    case 'bulk':
      return <BulkUnitScreen />
    case 'newBulkUnit':
      return <NewBulkUnitScreen />
    case 'previewRule':
      return <PreviewRuleCard />
    case 'demo':
      return <MyForm />
    case 'editNewRule':
      return <EditNewRule />
    case 'nocForm':
      return <NocFormsPage />
    case 'helpdesk':
      return <ComplaintDetailsPage />
    case 'socprofile':
      return <SocProfileScreen customUrl={customURL} />
    case 'shareCertificates':
      return <ShareCertificateWrapper memberId={param?.id as string} />
    case 'form':
    default:
      return <FormBuilder href={href} params={param} />
  }
}

export default PageTypeScreen
