// updateFormData.ts

import { isObject, updateNestedValue } from "@/utils/object";

export type Params = Record<string, any>;
export type Schema = Record<string, any>;
export type UISchema = Record<string, any>;
export type FormData = Record<string, any>;

/**
 * Updates the formData based on the provided params and uiSchema.
 *
 * @param params - The parameters containing fields to update.
 * @param _schema - The schema (unused in this function, kept for signature compatibility).
 * @param uiSchema - The UI schema object that defines widget options.
 * @param formData - The formData object to update.
 */
const updateFormData = (
  params: Params,
  _schema: Schema,
  uiSchema: UISchema,
  formData: FormData
): void => {
  if (!params) return;

  // Preprocess params to handle __title objects and extract referenced keys
  const processedParams: Params = { ...params };
  const keysToExclude = new Set<string>();

  Object.entries(params).forEach(([field, value]) => {
    if (isObject(value) && '__title' in value) {
      console.log(value);

      // Extract keys referenced in the template
      const templateKeys = value.__title.match(/{{(.*?)}}/g)?.map((match: string) => 
        match.replace(/[{}]/g, '').trim()
      ) || [];

      // Mark referenced keys for exclusion from main loop
      templateKeys.forEach((key: string) => {
        if (params.hasOwnProperty(key)) {
          keysToExclude.add(key);
        }
      });

      // Compute the title by replacing placeholders
      const computedTitle = value.__title.replace(/{{(.*?)}}/g, (_match: string, keyMatch: string) => {
        const key = keyMatch.trim();

        const replacement = params.hasOwnProperty(key)
          ? (params[key] || 0)
          : (formData.hasOwnProperty(key) ? formData[key] : 0);
          
        return String(replacement);
      });

      // Update the processed params with the computed title
      processedParams[field] = computedTitle;
    }
  });

  // Remove keys that were used as template variables
  keysToExclude.forEach(key => {
    delete processedParams[key];
  });

  // Main processing loop with the processed params
  Object.entries(processedParams).forEach(([field, value]) => {
    if (Array.isArray(value) && !uiSchema[field]?.["ui:options"]?.multiple && !["table", "array"].includes(String(uiSchema[field]?.["ui:options"]?.type))) {
      // If the value is an array but the widget doesn't support multiple selections,
      // join the array values into a comma-separated string.

      // console.log("in IF for ", field, "with value ", value, " and type", uiSchema[field]?.["ui:options"]?.type);

      formData[field] = value?.filter(Boolean).map((item) => String(item)).join(",");
    } else {
      // Coerce the value based on the type defined in the UI schema.
      const typeMapping: { [key: string]: any } = {
        boolean: Boolean(value),
        number: Number(value) || 0,
        integer: Number(value) || 0,
      };

      const uiType: string | undefined = uiSchema[field]?.["ui:options"]?.type;

      if (uiType && uiType in typeMapping) {
        // console.log("in IF for ", field, "with value ", value, " and type", uiType);
        updateNestedValue(formData, field.split("."), typeMapping[uiType]);
      } else {
        // console.log("in ELSE for ", field, "with value ", value, " and type", uiType);
        updateNestedValue(formData, field.split("."), value);
      }
    }
  });
}

export default updateFormData;
