import { useState, useEffect, useMemo } from 'react'

import { Autocomplete, TextField, Chip, CircularProgress } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import axios from 'axios'
import { styled } from '@mui/system'

import { descriptionId, getTemplate, getUiOptions } from '@rjsf/utils'
import type { FormContextType, RJSFSchema, StrictRJSFSchema } from '@rjsf/utils'

import Adornment from '../components/Adornment'
import { extractPathWithUnderscores, match, parse } from '@/utils/string'
import { updateNestedValue } from '@/utils/object'
import { replacePlaceholders } from '@/utils/replacePlaceholders'

// Utility to format the option label based on the provided array of label keys
const formatOptionLabel = (option, labelKeys) => {
  if (!labelKeys || labelKeys.length === 0) return ''

  return [...new Set(labelKeys.map(key => option[key?.field || key] || ''))].filter(Boolean).join(' - ') || ''
}

type Dependent = {
  field: string
  key?: string
}

interface GroupHeaderProps {
  level: number
}

const AutocompleteListItem = styled('li')({
  // Reset the padding from MUI's default class
  padding: '0 !important',
  margin: '0 !important',
  listStyle: 'none'
})

const GroupHeader = styled('div')<GroupHeaderProps>(({ theme, level = 0 }) => ({
  width: '100%',
  boxSizing: 'border-box',
  padding: '12px 16px',
  paddingLeft: 16 + level * 20,

  backgroundColor: theme.palette.grey[50],
  color: theme.palette.text.primary,
  borderBottom: `1px solid ${theme.palette.divider}`,
  fontWeight: 600,
  fontSize: '0.875rem',
  cursor: 'pointer',
  transition: 'background-color 0.2s ease',

  '&:hover': {
    backgroundColor: theme.palette.grey[100]
  },

  "[aria-selected='true'] &": {
    backgroundColor: 'transparent',
    color: theme.palette.text.primary
  }
}))

// Update GroupItems
const GroupItems = styled('div')<GroupHeaderProps>(({ theme, level = 0 }) => ({
  width: '100%',
  boxSizing: 'border-box',
  padding: '12px 16px',
  paddingLeft: 16 + level * 20,

  fontSize: '0.875rem',
  color: theme.palette.text.primary,
  cursor: 'pointer',
  transition: 'background-color 0.2s ease',
  borderBottom: `1px solid ${theme.palette.divider}`,

  '&:hover': {
    backgroundColor: theme.palette.action.hover
  },

  "[aria-selected='true'] &": {
    backgroundColor: 'transparent',
    color: theme.palette.text.primary
  },

  '&:last-child': {
    borderBottom: 'none'
  }
}))

// Recursive flattening of nested options
function flattenOptions(data: any[], labelKeys: string[] = ['title'], level = 0) {
  const flattened: any[] = []

  data.forEach(item => {
    const title = Array.isArray(labelKeys)
      ? labelKeys
          .map(k => item[k] ?? '')
          .filter(Boolean)
          .join(' - ')
      : ''

    const isParent = item?.rows && Array.isArray(item.rows)
    const { rows, ...rest } = item

    flattened.push({
      ...rest,
      title,
      level,
      isParent
    })

    if (isParent) {
      flattened.push(...flattenOptions(rows, labelKeys, level + 1))
    }
  })

  return flattened
}

const replaceFromDependant = (dependent: Dependent[] = [], item: Record<string, any>) => {
  const mappedValues: Record<string, any> = {}

  if (Array.isArray(dependent)) {
    for (const dep of dependent) {
      const keyToUse = dep?.key || dep?.field

      if (keyToUse && keyToUse in item) {
        mappedValues[dep.field] = item[keyToUse]
      }
    }
  }

  return {
    id: item?.id,
    ...mappedValues,
    ...(item?.rows &&
      Array.isArray(item?.rows) && {
        rows: item?.rows?.map(row => replaceFromDependant(dependent, row))
      }),
    ...(item?.children &&
      Array.isArray(item?.children) && {
        children: item?.children?.map(child => replaceFromDependant(dependent, child))
      })
  }
}

const findItemRecursive = (data: any[], id: string) => {
  for (const item of data) {
    if (item.id === id) return item

    if (item.rows) {
      const found = findItemRecursive(item.rows, id)

      if (found) return found
    }
  }

  return null
}

interface ExtraOptions extends FormContextType {
  startAdornment?: any[]
  endAdornment?: any[]
  labelKeys?: string[]
  limitTags?: number
}

function TypeaheadField<T = any, S extends StrictRJSFSchema = RJSFSchema, F extends ExtraOptions = any>({
  schema,
  uiSchema,
  formData,
  onChange,
  idSchema,
  formContext,
  errorSchema,
  registry,
  name,
  disabled
}) {
  const description = errorSchema?.id?.__errors?.join(', ') || uiSchema?.['ui:description'] || schema?.description || ''

  const options = getUiOptions<T, S, F>(uiSchema)
  const DescriptionFieldTemplate = getTemplate('DescriptionFieldTemplate', registry, options)

  const isError = errorSchema?.id?.__errors?.join(', ') || errorSchema?.['0']?.id?.__errors?.join(', ')

  const {
    apiPath,
    labelKeys = ['title'],
    minLength = 3,
    multiple = false,
    onMount = false,
    defaultParams = [],
    dependent = [],
    update = null,
    loadedOptions = null,
    limitTags = 1,
    allowParentSelection = false,
    allowCreate = null
  } = options

  // Ensure labelKeys is an array (using useMemo)
  const safeLabelKeys = useMemo(() => {
    return Array.isArray(labelKeys) ? labelKeys : typeof labelKeys === 'string' ? [labelKeys] : []
  }, [labelKeys])

  const [searchQuery, setSearchQuery] = useState('')
  const [selected, setSelected] = useState(multiple ? [] : null)

  const params = defaultParams?.reduce((acc, param) => {
    const value = formContext?.formRef?.current?.state?.formData[param] ?? null

    return { ...acc, [param]: value }
  }, {})

  // Query to load initial options on mount if onMount is true
  const { data: initialOptions, isLoading: isInitialLoading } = useQuery({
    queryKey: [apiPath, loadedOptions],
    queryFn: async () => {
      try {
        let data = []

        if (Array.isArray(loadedOptions)) {
          data = loadedOptions
        } else if (apiPath && typeof apiPath === 'string') {
          const formValues = formContext?.formRef?.current?.state?.formData
          const api = replacePlaceholders(apiPath, formValues)

          const response = await axios.get(api, {
            baseURL: process.env.NEXT_PUBLIC_API_URL,
            withCredentials: true
          })

          data = response?.data?.data
        }

        if (Array.isArray(data)) {
          const labels = safeLabelKeys.map(item =>
            item?.field ? { field: item.field, key: item?.key } : { field: item, key: item }
          )

          const safeDependent = Array.isArray(dependent) ? dependent : [dependent]

          const updated = data.map(item => replaceFromDependant([...safeDependent, ...labels], item))

          let addAllowCreateItem = null

          if (allowCreate) {
            const label = labels?.[0]?.field ?? "title"

            addAllowCreateItem = {
              [label]: allowCreate?.label ?? 'Add New',
              id: allowCreate?.id ?? 'new'
            }
          }

          const finalOptions = allowCreate ? [addAllowCreateItem, ...updated] : updated;

          return flattenOptions(finalOptions, safeLabelKeys)
        }

        return []
      } catch (error) {
        console.warn('Error loading initial options:', error)

        return []
      }
    },
    enabled: !!onMount || (Array.isArray(loadedOptions) && !!loadedOptions.length)
  })

  // Query to load search results when the user types (enabled when searchQuery length meets minLength)
  const { data: searchOptions, isLoading: isSearchLoading } = useQuery({
    queryKey: [searchQuery, apiPath, params],
    queryFn: async () => {
      const formValues = formContext?.formRef?.current?.state?.formData

      const params = (Array.isArray(defaultParams) ? defaultParams : []).reduce(
        (acc, param) => ({ ...acc, [param]: formValues[param] ?? null }),
        {}
      )

      const api = replacePlaceholders(String(apiPath), formValues)

      const response = await axios.get(api, {
        params: { keyword: searchQuery, ...params },
        baseURL: process.env.NEXT_PUBLIC_API_URL,
        withCredentials: true
      })

      const data = response?.data?.data

      if (Array.isArray(data)) {
        return flattenOptions(data, safeLabelKeys)
      }

      return []
    },
    enabled: searchQuery.length >= Number(minLength),
    staleTime: 2 * 60 * 1000
  })

  // Determine which options to display based on searchQuery and onMount flag
  const optionData = useMemo(
    () =>
      searchQuery.length >= Number(minLength)
        ? searchOptions || []
        : onMount
          ? initialOptions || []
          : schema.oneOf || [],
    [searchQuery.length, minLength, searchOptions, onMount, initialOptions, schema.oneOf]
  )

  // Sync selected value from formData and optionData
  useEffect(() => {
    if (!optionData) return

    if (formData?.id || Array.isArray(formData)) {
      if (multiple && selected?.length) return
      if (!multiple && selected?.id === formData?.id) return
    }

    if (Array.isArray(formData) || multiple) {
      const newSelected = (formData || [])
        .map(dataItem => optionData.find(item => item.id === dataItem?.id))
        .filter(Boolean)

      setSelected(newSelected)
    } else if (formData?.id && !selected) {
      const option = optionData.find(item => item.id === formData?.id) || null

      // setSelected();

      handleSelectionChange(null, option)
    } else if (formData !== null && !isNaN(formData)) {
      const item = optionData.find(item => item.id === formData) || findItemRecursive(optionData, formData) || null

      handleSelectionChange(null, item)

      // onChange(item);
    } else {
      setSelected(null)
    }
  }, [formData, optionData, multiple, onChange, selected])

  const handleSelectionChange = (_event, value) => {
    if (!value) return
    setSelected(value)
    onChange(value)

    // ! BUG: value can be array or just id, need some changes 343:68
    if (update) {
      const state = { ...formContext?.formRef?.current?.state?.formData }
      const fieldPath = extractPathWithUnderscores(idSchema?.$id, name)

      updateNestedValue(state, fieldPath.concat(name), value || {})
      Object.entries(update).forEach(([fieldName, keyName]) => {
        updateNestedValue(state, fieldPath.concat(fieldName), value[keyName] ?? 0)
      })

      formContext?.formRef?.current?.onChange(state)
    }
  }

  const adornments = params => ({
    ...(options?.startAdornment &&
      Array.isArray(options?.startAdornment) && {
        startAdornment: (
          <>
            {params?.InputProps?.startAdornment}
            {options?.startAdornment.map((adornment, index) => (
              <Adornment key={index} adornment={adornment} position='start' />
            ))}
          </>
        )
      }),
    ...(options?.endAdornment &&
      Array.isArray(options?.endAdornment) && {
        endAdornment: (
          <>
            {isInitialLoading || isSearchLoading ? <CircularProgress color='inherit' size={15} /> : null}
            {options?.endAdornment.map((adornment, index) => (
              <Adornment key={index} adornment={adornment} position='end' />
            ))}
            {params?.InputProps?.endAdornment}
          </>
        )
      })
  })

  return (
    <div id={idSchema?.$id}>
      <Autocomplete
        sx={{
          minWidth: 235,
          '& .MuiAutocomplete-listbox': {
            padding: 0,
            maxHeight: '300px',
            '& .MuiAutocomplete-option': {
              padding: 0,
              minHeight: 'auto'
            }
          },
          '& .MuiAutocomplete-paper': {
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e0e0e0',
            borderRadius: '8px',
            marginTop: '4px'
          },
          '& .MuiAutocomplete-noOptions': {
            padding: '12px 16px',
            fontSize: '0.875rem',
            color: 'text.secondary'
          }
        }}
        multiple={!!multiple}
        options={optionData}
        getOptionLabel={option => formatOptionLabel(option, labelKeys)}
        getOptionKey={option => option.id}
        value={selected}
        groupBy={null}
        disabled={disabled}
        getOptionDisabled={option => option.isParent && !allowParentSelection}
        renderOption={(props, option) => {
          const isSelected = props['aria-selected']

          // We only want the special "Header" style if the item is a parent
          // AND it is NOT currently selected.
          const showAsHeader = option.isParent && !isSelected

          return (
            <AutocompleteListItem {...props} key={option.id}>
              {showAsHeader ? (
                <GroupHeader level={option.level || 0}>{option.title}</GroupHeader>
              ) : (
                // Render the normal item style for:
                // 1. All child items (always)
                // 2. Parent items that are currently selected
                <GroupItems level={option.level || 0}>
                  <div>
                    {parse(option.title, match(option.title, searchQuery)).map((part, index) => (
                      <span
                        key={index}
                        style={{
                          fontWeight: part.highlight ? 700 : 400
                        }}
                      >
                        {part.text}
                      </span>
                    ))}
                  </div>
                </GroupItems>
              )}
            </AutocompleteListItem>
          )
        }}
        isOptionEqualToValue={(option, value) => option.id === value.id}
        onChange={handleSelectionChange}
        slotProps={{
          clearIndicator: {
            onClick: () => {
              const emptyValue = multiple ? [] : null

              setSelected(emptyValue)
              onChange(emptyValue)
            }
          }
        }}
        loading={isInitialLoading || isSearchLoading}
        renderInput={params => (
          <TextField
            {...params}
            placeholder={uiSchema?.['ui:placeholder']}
            label={uiSchema?.['ui:title'] || schema.title}
            error={!!isError}
            helperText={
              isError || (
                <DescriptionFieldTemplate
                  id={descriptionId(idSchema)}
                  description={description}
                  schema={schema}
                  uiSchema={uiSchema}
                  registry={registry}
                />
              )
            }
            onChange={event => setSearchQuery(event.target.value)}
            slotProps={{
              input: {
                ...params.InputProps,
                ...adornments(params)
              }
            }}
          />
        )}
        limitTags={Number(limitTags)}
        renderTags={(value, getTagProps) =>
          value.map((option, index) => (
            <Chip label={formatOptionLabel(option, labelKeys)} {...getTagProps({ index })} key={option?.id} />
          ))
        }
      />
    </div>
  )
}

export default TypeaheadField
