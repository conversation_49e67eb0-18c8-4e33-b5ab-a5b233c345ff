import React, { useState, memo } from "react";

import type { ArrayFieldTemplateProps } from "@rjsf/utils";
import {
  Paper,
  IconButton,
  Tooltip,
  Button,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Box,
  Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";

function ArrayFieldTemplate(props: ArrayFieldTemplateProps) {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  // Get field names from schema properties
  const getFieldNames = () => {
    const itemSchema = props.schema.items;
    
    if (itemSchema && typeof itemSchema === 'object' && itemSchema.properties) {
      return Object.keys(itemSchema.properties);
    }

    return [];
  };

  // Get field labels from schema
  const getFieldLabel = (fieldName: string) => {
    const itemSchema = props.schema.items;

    if (itemSchema && typeof itemSchema === 'object' && itemSchema.properties) {
      const fieldSchema = itemSchema.properties[fieldName];
      
      if (fieldSchema && typeof fieldSchema === 'object') {
        return fieldSchema.title || fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
      }
    }

    return fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
  };

  const fieldNames = getFieldNames();

  const handleAdd = () => {
    if (!props.canAdd) return;

    // Get the current form data from the last item (add form)
    // const addFormData = props.items.length > 0 ? 
      // props.items[props.items.length - 1].children.props.formData || {} : {};

    // Validate using RJSF's standard validation
    // const validation = validateCurrentFormData(addFormData);
    
    // if (!validation.isValid) {
    //   setValidationErrors(validation.errors);
    //   return;
    // }

    // Clear errors and use RJSF's onAddClick event
    props.onAddClick();
  };

  const handleEdit = (index: number) => {
    setEditingIndex(index);
  };

  const handleDelete = (index: number) => {
    // Use the item's onDropIndexClick event handler
    const element = props.items[index];

    if (element?.hasRemove && element.onDropIndexClick) {
      element.onDropIndexClick(index)();
    }
    
    // Reset editing state if we deleted the item being edited
    if (editingIndex === index) {
      setEditingIndex(null);
    }
  };

  const handleSave = () => {
    // if (editingIndex !== null) {
      // Get the form data from the item being edited
      // const editFormData = props.items[editingIndex]?.children.props.formData || {};
      
      // Validate using RJSF's standard validation
      // const validation = validateCurrentFormData(editFormData);
      
      // if (!validation.isValid) {
      //   setValidationErrors(validation.errors);

      //   return;
      // }
    // }
    
    // Clear errors and save
    setEditingIndex(null);
  };

  const handleCancel = () => {
    setEditingIndex(null);
  };

  // Filter saved items (exclude incomplete add forms)
  const savedItems = props.items.filter((element, index) => {
    const formData = element.children.props.formData || {};
    
    // If this is the last item and we're not editing, check if it's just an empty add form
    if (index === props.items.length - 1 && editingIndex === null) {
      const hasData = Object.keys(formData).some(key => 
        formData[key] !== undefined && 
        formData[key] !== '' && 
        formData[key] !== null
      );

      return hasData;
    }
    
    // For other items, include them if they have data
    return Object.keys(formData).some(key => 
      formData[key] !== undefined && 
      formData[key] !== '' && 
      formData[key] !== null
    );
  });

  // Get current form data for validation
  const getCurrentFormData = () => {
    if (editingIndex !== null && props.items[editingIndex]) {
      return props.items[editingIndex].children.props.formData || {};
    } else if (props.items.length > 0) {
      return props.items[props.items.length - 1].children.props.formData || {};
    }

    return {};
  };

  const currentFormData = getCurrentFormData();

  const hasFormData = Object.keys(currentFormData).length > 0 && 
    Object.keys(currentFormData).some(key => 
      currentFormData[key] !== undefined && 
      currentFormData[key] !== '' && 
      currentFormData[key] !== null
    );

  return (
    <div className={props.className}>
      {/* Form Section */}
      <Paper elevation={0} sx={{ p: 3, mb: 3, border: '1px solid #e0e0e0', borderRadius: 1 }}>
        <Typography variant="h6" gutterBottom>
          {editingIndex !== null ? 'Edit Entry' : 'Add New Entry'}
        </Typography>
        
        <Grid container spacing={2}>
          <Grid size="grow">
            {editingIndex !== null && props.items[editingIndex] ? (
              props.items[editingIndex].children
            ) : props.items.length > 0 ? (
              props.items[props.items.length - 1].children
            ) : (
              <Typography variant="body2" color="textSecondary">
                Click Add to create a new entry
              </Typography>
            )}
          </Grid>
          
          {/* Action Buttons */}
          <Grid size={12}>
            <Box display="flex" justifyContent="flex-end" gap={1} mt={2}>
              {editingIndex !== null ? (
                <>
                  <Button variant="outlined" onClick={handleCancel}>
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleSave}
                    disabled={!hasFormData || props.disabled || props.readonly}
                  >
                    Save
                  </Button>
                </>
              ) : (
                <>
                  {props.canAdd && (
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleAdd}
                      disabled={!hasFormData || props.disabled || props.readonly}
                    >
                      Add
                    </Button>
                  )}
                </>
              )}
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Table Section */}
      <Paper elevation={0} sx={{ border: '1px solid #e0e0e0', borderRadius: 1 }}>
        <Table sx={{ '& .MuiTableCell-root': { borderBottom: '1px solid #e0e0e0' } }}>
          <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
            <TableRow>
              <TableCell sx={{ fontWeight: 600, py: 2 }}>Sr No.</TableCell>
              {fieldNames.map((fieldName) => (
                <TableCell key={fieldName} sx={{ fontWeight: 600, py: 2 }}>
                  {getFieldLabel(fieldName)}
                </TableCell>
              ))}
              <TableCell align="center" sx={{ fontWeight: 600, py: 2 }}>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {savedItems.length > 1 ? (
              savedItems.slice(0, -1).map((element, index) => {
                const formData = element.children.props.formData || {};
                const isBeingEdited = editingIndex === index;
                
                return (
                  <TableRow 
                    key={element.key || index}
                    sx={{
                      backgroundColor: isBeingEdited ? '#e3f2fd' : 'transparent',
                      '&:hover': {
                        backgroundColor: isBeingEdited ? '#e3f2fd' : '#f5f5f5'
                      }
                    }}
                  >
                    <TableCell sx={{ py: 1.5 }}>{index + 1}</TableCell>
                    {fieldNames.map((fieldName) => (
                      <TableCell key={fieldName} sx={{ py: 1.5 }}>
                        {formData[fieldName] || '-'}
                      </TableCell>
                    ))}
                    <TableCell align="center" sx={{ py: 1.5 }}>
                      <Tooltip title="Edit">
                        <IconButton
                          size="small"
                          onClick={() => handleEdit(index)}
                          color={isBeingEdited ? "secondary" : "primary"}
                          disabled={isBeingEdited || props.disabled || element.disabled}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      {element.hasRemove && (
                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            onClick={() => handleDelete(index)}
                            color="error"
                            disabled={props.disabled || element.disabled || props.readonly || element.readonly}
                            sx={{ ml: 1 }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell 
                  colSpan={fieldNames.length + 2} 
                  align="center" 
                  sx={{ py: 4, fontStyle: 'italic', color: 'text.secondary' }}
                >
                  No mapping data.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </Paper>

      {/* Hidden items for RJSF state management */}
      <Box sx={{ display: 'none' }}>
        {props.items.map((element, index) => {
          // Don't duplicate the currently visible form
          if (index === editingIndex || index === props.items.length - 1) return null;

          return <div key={element.key || index}>{element.children}</div>;
        })}
      </Box>
    </div>
  );
}

export default memo(ArrayFieldTemplate);
