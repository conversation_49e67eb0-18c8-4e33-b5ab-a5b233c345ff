import type { JSX } from 'react';

import type {
    DescriptionFieldProps,
    RJSFSchema,
    StrictRJSFSchema,
    FormContextType,
} from '@rjsf/utils';
import { Typography } from '@mui/material';

function DescriptionFieldTemplate<
    T = any,
    S extends StrictRJSFSchema = RJSFSchema,
    F extends FormContextType = any
>(props: DescriptionFieldProps<T, S, F>): null | JSX.Element {
    const { description = "", id = "", schema: { type = "" } } = props;

    // If description is empty or id is empty or type is "null", return null
    // type null is used to indicate that the field is not input
    if (!description || !id || type !== "null") return null

    return (
        <Typography component="p" id={id} className="field-description" variant='subtitle2'>
            {description}
        </Typography>
    );
}


export default DescriptionFieldTemplate