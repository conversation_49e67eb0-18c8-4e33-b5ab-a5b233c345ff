/**
 * Title: changed
 * Description: Returns true if the JSON-stringified representations of two values differ.
 *
 * @param factValue - The first value to compare (any type).
 * @param compareValue - The second value to compare against (any type).
 * @returns Whether the two inputs are different when stringified.
 *
 * @example
 * changed(1, 2); // true
 * @example
 * changed({ a: 1 }, { a: 1 }); // false
 * @example
 * changed([1, 2], [2, 1]); // true
 */
export function changed(factValue: unknown, compareValue: unknown): boolean {
    return JSON.stringify(factValue) !== JSON.stringify(compareValue);
}

/**
 * Title: notChanged
 * Description: Returns true if the JSON-stringified representations of two values are identical.
 *
 * @param factValue - The first value to compare (any type).
 * @param compareValue - The second value to compare against (any type).
 * @returns Whether the two inputs are the same when stringified.
 *
 * @example
 * notChanged(1, 1); // true
 * @example
 * notChanged({ x: 10 }, { x: 20 }); // false
 * @example
 * notChanged(["a"], ["a"]); // true
 */
export function notChanged(factValue: unknown, compareValue: unknown): boolean {
    return JSON.stringify(factValue) === JSON.stringify(compareValue);
}

/**
 * Title: truthy
 * Description: Returns true if the given value is truthy in JavaScript context.
 *
 * @param factValue - The value to test for truthiness.
 * @returns Boolean indicating if the input is truthy.
 *
 * @example
 * truthy(1); // true
 * @example
 * truthy("hello"); // true
 * @example
 * truthy(0); // false
 */
export function truthy(factValue: unknown): boolean {
    return !!factValue;
}

/**
 * Title: falsy
 * Description: Returns true if the given value is falsy in JavaScript context.
 *
 * @param factValue - The value to test for falsiness.
 * @returns Boolean indicating if the input is falsy.
 *
 * @example
 * falsy(0); // true
 * @example
 * falsy(""); // true
 * @example
 * falsy(123); // false
 */
export function falsy(factValue: unknown): boolean {
    return !factValue;
}

/**
 * Title: isNumber
 * Description: Checks whether the input is a valid number (not NaN).
 *
 * @param factValue - The value to test (should be a number).
 * @returns True if the input is of type number and not NaN.
 *
 * @example
 * isNumber(42); // true
 * @example
 * isNumber(NaN); // false
 * @example
 * isNumber("42"); // false
 */
export function isNumber(factValue: unknown): boolean {
    return typeof factValue === 'number' && !Number.isNaN(factValue);
}

/**
 * Title: isArray
 * Description: Checks if the given value is an array.
 *
 * @param factValue - The value to test.
 * @returns True if the input is an array.
 *
 * @example
 * isArray([1, 2, 3]); // true
 * @example
 * isArray("not an array"); // false
 * @example
 * isArray({}); // false
 */
export function isArray(factValue: unknown): boolean {
    return Array.isArray(factValue);
}

/**
 * Title: exists
 * Description: Checks whether the given value is not undefined.
 *
 * @param factValue - The value to test.
 * @returns True if the input is defined (not undefined).
 *
 * @example
 * exists(0); // true
 * @example
 * exists(undefined); // false
 * @example
 * exists(null); // true
 */
export function exists(factValue: unknown): boolean {
    return factValue !== undefined;
}

/**
 * Title: inRange
 * Description: Validates that a number falls within a specified min and max range.
 *
 * @param factValue - The value to test (should be numeric).
 * @param minMax - A tuple [min, max] defining the allowed range.
 * @returns True if factValue is between min and max (inclusive).
 *
 * @example
 * inRange(5, [1, 10]); // true
 * @example
 * inRange(0, [1, 5]); // false
 * @example
 * inRange(3, [3, 3]); // true
 */
export function inRange(
    factValue: unknown,
    minMax: [number, number] = [0, 0]
): boolean {
    if (
        !Array.isArray(minMax) ||
        minMax.length !== 2 ||
        Number.isNaN(Number(minMax[0])) ||
        Number.isNaN(Number(minMax[1])) ||
        minMax[0] > minMax[1]
    ) {
        return false;
    }

    return typeof factValue === 'number' && factValue >= minMax[0] && factValue <= minMax[1];
}

/**
 * Title: filterBy
 * Description: Filters an array of objects by matching key–value pairs, returns true if any match is found.
 *
 * @param arrayOfObjects - The array to filter.
 * @param filter - An object whose entries define the match criteria.
 * @returns True if at least one object matches all filter conditions.
 *
 * @example
 * filterBy([{a:1},{a:2}], {a:2}); // true
 * @example
 * filterBy([{x:3},{y:4}], {y:5}); // false
 * @example
 * filterBy([], {a:1}); // false
 */
export function filterBy(
    arrayOfObjects: unknown,
    filter: Record<string, unknown>
): boolean {
    if (!Array.isArray(arrayOfObjects) || typeof filter !== 'object' || filter === null) {
        return false;
    }

    const filtered = (arrayOfObjects as Record<string, unknown>[]).filter(obj =>
        Object.entries(filter).every(([key, value]) => obj[key] === value)
    );

    return filtered.length > 0;
}

/**
 * Title: isInArray
 * Description: Checks if one non-array value is included in another array value.
 *
 * @param a - One of the values (array or single value).
 * @param b - The other value (array or single value).
 * @returns True if the non-array value is found in the array.
 *
 * @example
 * isInArray([1,2,3], 2); // true
 * @example
 * isInArray('x', ['x','y']); // true
 * @example
 * isInArray([1], [1]); // false
 */
export function isInArray(
    a: unknown,
    b: unknown
): boolean {
    if (Array.isArray(a) && !Array.isArray(b)) {
        return (a as unknown[]).includes(b);
    }

    if (Array.isArray(b) && !Array.isArray(a)) {
        return (b as unknown[]).includes(a);
    }

    return false;
}

/**
 * Title: isNotInArray
 * Description: Checks if one non-array value is not included in another array value.
 *
 * @param a - One of the values (array or single value).
 * @param b - The other value (array or single value).
 * @returns True if the non-array value is not found in the array.
 *
 * @example
 * isNotInArray([1,2,3], 4); // true
 * @example
 * isNotInArray('z', ['x','y']); // true
 * @example
 * isNotInArray([1], [1]); // false
 */
export function isNotInArray(
    a: unknown,
    b: unknown
): boolean {
    if (Array.isArray(a) && !Array.isArray(b)) {
        return !(a as unknown[]).includes(b);
    }

    if (Array.isArray(b) && !Array.isArray(a)) {
        return !(b as unknown[]).includes(a);
    }

    return false;
}

/**
 * Title: greaterThanInArray
 * Description: Returns true if in two same-length arrays, any numeric item in the first is greater than the corresponding item in the second.
 *
 * @param a - First array of values.
 * @param b - Second array of values.
 * @returns True if any element of `a` is a number greater than the corresponding element in `b`.
 *
 * @example
 * greaterThanInArray([2,3], [1,4]); // true (2 > 1)
 * @example
 * greaterThanInArray([1,1], [2,2]); // false
 * @example
 * greaterThanInArray([5], [5]); // false
 */
export function greaterThanInArray(
    a: unknown,
    b: unknown
): boolean {
    if (!Array.isArray(a) || !Array.isArray(b) || a.length !== b.length) {
        return false;
    }

    return (a as unknown[]).some((item, idx) => {
        const other = (b as unknown[])[idx];

        return typeof item === 'number' && typeof other === 'number' && item > other;
    });
}

/**
 * Title: isArrayEmpty
 * Description: Checks if an array is empty, optionally skipping falsy entries first.
 *
 * @param array - The value to test (should be an array).
 * @param skipFalsyEntriesFirst - If true, remove falsy items before checking length.
 * @returns True if the array (after optional filtering) has zero length.
 *
 * @example
 * isArrayEmpty([], false); // true
 * @example
 * isArrayEmpty([0, "", null], true); // true
 * @example
 * isArrayEmpty([1], false); // false
 */
export function isArrayEmpty(
    array: unknown,
    skipFalsyEntriesFirst = false
): boolean {
    if (!Array.isArray(array)) return false;
    const arr = skipFalsyEntriesFirst ? (array.filter(item => !item) as unknown[]) : array;

    return (arr as unknown[]).length === 0;
}

/**
 * Title: isArrayNotEmpty
 * Description: Checks whether the provided value is an array containing one or more items,
 *              optionally skipping all falsy entries before performing the length check.
 *
 * @param array - The value to test. Should be an array (of any element type).
 * @param skipFalsyEntriesFirst - If `true`, all falsy values (`0`, `""`, `null`, `undefined`, `false`, etc.)
 *                                will be removed before checking length. Defaults to `false`.
 * @returns `true` if `array` is a real array and, after optional filtering, its length is > 0; otherwise `false`.
 *
 * @example
 * isArrayNotEmpty([1, 2, 3]);             // true  (three numeric items)
 * @example
 * isArrayNotEmpty([]);                    // false (empty array)
 * @example
 * isArrayNotEmpty([0, "", null], false);  // true  (length is 3, even though all are falsy)
 * @example
 * isArrayNotEmpty([0, "", null], true);   // false (after dropping falsy entries, no items remain)
 * @example
 * isArrayNotEmpty(["hello"], true);       // true  (non‑falsy element remains)
 */
export function isArrayNotEmpty(
    array: unknown,
    skipFalsyEntriesFirst = false
): boolean {
    if (!Array.isArray(array)) {
        return false;
    }

    const items = skipFalsyEntriesFirst
        ? (array.filter(item => Boolean(item)) as unknown[])
        : array;

    return items.length > 0;
}
