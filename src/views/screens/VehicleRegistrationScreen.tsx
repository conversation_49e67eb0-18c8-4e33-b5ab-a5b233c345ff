'use client';

import React, { useState, useEffect } from 'react';

import axios from 'axios';
import { toast } from 'react-toastify';
import {
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Divider,
  CircularProgress,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

interface FormValues {
  unitNumber: string;
  building: string;
  floor: string;
  flat: string;
}

interface UnitData {
  id: string;
  unit_number: string;
  building_unit?: string;
  building_name?: string;
  floor_number?: string;
  unit_flat_number?: string;
  soc_building_name?: string;
  soc_building_floor?: number;
}

interface BuildingData {
  id: string;
  soc_building_name: string;
  floor_array?: number[];
}

const VehicleRegistrationScreen = () => {
  const { control, handleSubmit, reset, watch, setValue } = useForm<FormValues>();
  const [units, setUnits] = useState<UnitData[]>([]);
  const [loadingUnits, setLoadingUnits] = useState(false);
  const [unitsError, setUnitsError] = useState<string | null>(null);
  
  const [buildings, setBuildings] = useState<BuildingData[]>([]);
  const [loadingBuildings, setLoadingBuildings] = useState(false);
  const [buildingsError, setBuildingsError] = useState<string | null>(null);

  const [flats, setFlats] = useState<UnitData[]>([]);
  const [loadingFlats, setLoadingFlats] = useState(false);
  const [flatsError, setFlatsError] = useState<string | null>(null);

  // Auto-fill state to track when fields are automatically populated
  const [isAutoFilled, setIsAutoFilled] = useState(false);

  // Submission states
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Watch for building and floor selection changes
  const selectedBuilding = watch('building');
  const selectedFloor = watch('floor');
  const selectedFlat = watch('flat');
  const selectedUnitNumber = watch('unitNumber');

  // Auto-populate building and floor when unit number is selected
  useEffect(() => {
    if (selectedUnitNumber && units.length > 0 && buildings.length > 0) {
      const selectedUnit = units.find(unit => 
        (unit.building_unit || unit.unit_number) === selectedUnitNumber
      );
      
      console.log('Selected unit:', selectedUnit);
      console.log('Available buildings:', buildings);
      console.log('Selected unit number:', selectedUnitNumber);
      
      if (selectedUnit) {
        // Set building if available - need to find matching building from buildings array
        if (selectedUnit.soc_building_name || selectedUnit.building_name) {
          const unitBuildingName = selectedUnit.soc_building_name || selectedUnit.building_name;
          
          // Find the building that matches the unit's building name
          const matchingBuilding = buildings.find(building => 
            building.soc_building_name === unitBuildingName ||
            building.soc_building_name.toLowerCase() === unitBuildingName?.toLowerCase()
          );
          
          if (matchingBuilding) {
            console.log('Setting building to:', matchingBuilding.soc_building_name);
            setValue('building', matchingBuilding.soc_building_name);
          } else {
            console.log('No matching building found for:', unitBuildingName);
            console.log('Available building names:', buildings.map(b => b.soc_building_name));
          }
        }
        
        // Set floor if available
        if (selectedUnit.soc_building_floor !== undefined || selectedUnit.floor_number) {
          const floorValue = selectedUnit.soc_building_floor?.toString() || selectedUnit.floor_number?.toString() || '';

          console.log('Setting floor to:', floorValue);
          setValue('floor', floorValue);
        }

        // Set flat if available (unit already specifies the flat)
        if (selectedUnit.unit_flat_number) {
          console.log('Setting flat to:', selectedUnit.unit_flat_number);
          setValue('flat', selectedUnit.unit_flat_number);
        }
      } else {
        console.log('No matching unit found for:', selectedUnitNumber);
      }
    }
  }, [selectedUnitNumber, units, buildings, setValue]);

  // Get floor array for selected building
  const getFloorArray = () => {
    if (!selectedBuilding) return [];
    
    const building = buildings.find(b => b.soc_building_name === selectedBuilding);
    
    return building?.floor_array || [];
  };

  // Fetch flats based on selected building and floor
  useEffect(() => {
    const fetchFlats = async () => {
      if (!selectedBuilding || !selectedFloor) {
        setFlats([]);
        
        return;
      }

      setLoadingFlats(true);
      setFlatsError(null);

      try {
        // Find the building ID for the selected building name
        const selectedBuildingData = buildings.find(b => b.soc_building_name === selectedBuilding);
        
        if (!selectedBuildingData) {
          throw new Error('Building not found');
        }

        // Use query parameters for building_id and floor
        const params = new URLSearchParams({
          building_id: selectedBuildingData.id,
          soc_building_floor: selectedFloor
        });

        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/units/list?${params.toString()}`, {
          withCredentials: true,
        });

        if (response.data) {
          const unitsData = Array.isArray(response.data) ? response.data : response.data.data || [];
          
          // Filter to only show units with flat numbers (API should already filter by building and floor)
          const filteredFlats = unitsData.filter((unit: UnitData) => unit.unit_flat_number);
          
          setFlats(filteredFlats);
        }
      } catch (error: any) {
        const errorMessage = error.response?.data?.message || 'Error fetching flats. Please try again.';
        
        console.error('Error fetching flats:', error);
        setFlatsError(errorMessage);
      } finally {
        setLoadingFlats(false);
      }
    };

    fetchFlats();
  }, [selectedBuilding, selectedFloor, buildings]);

  // Fetch units data from API
  useEffect(() => {
    const fetchUnits = async () => {
      setLoadingUnits(true);
      setUnitsError(null);

      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/v2/admin/units/list`, {
          withCredentials: true,
        });

        if (response.data) {
          // Assuming the API returns an array of units or has a data property
          const unitsData = Array.isArray(response.data) ? response.data : response.data.data || [];
          
          setUnits(unitsData);
        }
      } catch (error: any) {
        const errorMessage = error.response?.data?.message || 'Error fetching units. Please try again.';
        
        console.error('Error fetching units:', error);
        setUnitsError(errorMessage);
      } finally {
        setLoadingUnits(false);
      }
    };

    fetchUnits();
  }, []);

  // Fetch buildings data from API
  useEffect(() => {
    const fetchBuildings = async () => {
      setLoadingBuildings(true);
      setBuildingsError(null);

      try {
        const response = await axios.get(`${process.env.NEXT_PUBLIC_API_URL}/admin/building/list`, {
          withCredentials: true,
        });

        if (response.data) {
          // Assuming the API returns an array of buildings or has a data property
          const buildingsData = Array.isArray(response.data) ? response.data : response.data.data || [];
          
          setBuildings(buildingsData);
        }
      } catch (error: any) {
        const errorMessage = error.response?.data?.message || 'Error fetching buildings. Please try again.';
        
        console.error('Error fetching buildings:', error);
        setBuildingsError(errorMessage);
      } finally {
        setLoadingBuildings(false);
      }
    };

    fetchBuildings();
  }, []);

  // Reset floor and flat selection when building changes (but not when unit is selected)
  useEffect(() => {
    // Only reset if building change is not triggered by unit selection
    if (!selectedUnitNumber) {
      setValue('floor', '');
      setValue('flat', '');
    }
  }, [selectedBuilding, selectedUnitNumber, setValue]);

  // Reset flat selection when floor changes (but not when unit is selected)
  useEffect(() => {
    // Only reset if floor change is not triggered by unit selection
    if (!selectedUnitNumber) {
      setValue('flat', '');
    }
  }, [selectedFloor, selectedUnitNumber, setValue]);

  // Debug effect to track when unit number changes
  useEffect(() => {
    console.log('🔄 Unit Number changed:', selectedUnitNumber);
    console.log('🔄 Is Auto Filled:', isAutoFilled);
  }, [selectedUnitNumber, isAutoFilled]);

  // Clear building and floor when unit number is cleared (but not when auto-filled from reverse selection)
  useEffect(() => {
    if (!selectedUnitNumber && !isAutoFilled) {
      // Clear the fields when unit is deselected and it's not from reverse auto-fill
      console.log('🧹 Clearing building, floor, and flat because unit was cleared manually');
      setValue('building', '');
      setValue('floor', '');
      setValue('flat', '');
    }
  }, [selectedUnitNumber, setValue, isAutoFilled]);

  // Reverse auto-population: Find and auto-select unit when building, floor, and flat are selected
  useEffect(() => {
    if (selectedBuilding && selectedFloor && selectedFlat && !selectedUnitNumber && units.length > 0) {
      console.log('🔍 Reverse auto-population triggered:');
      console.log('Building:', selectedBuilding);
      console.log('Floor:', selectedFloor);
      console.log('Flat:', selectedFlat);
      console.log('Units available:', units.length);
      console.log('All units:', units);

      // Add a small delay to ensure all data is loaded and state is stable
      const timeoutId = setTimeout(() => {
        // Find the matching unit in the units array
        const matchingUnit = units.find(unit => {
          // Check if the unit matches the selected building name
          const unitBuildingName = unit.soc_building_name || unit.building_name;
          const unitFloor = unit.soc_building_floor?.toString() || unit.floor_number?.toString();
          const unitFlat = unit.unit_flat_number;

          console.log('Checking unit:', {
            id: unit.id,
            unitNumber: unit.building_unit || unit.unit_number,
            unitBuildingName,
            unitFloor,
            unitFlat,
            matches: {
              building: unitBuildingName === selectedBuilding,
              floor: unitFloor === selectedFloor,
              flat: unitFlat === selectedFlat
            }
          });

          return unitBuildingName === selectedBuilding && 
                 unitFloor === selectedFloor && 
                 unitFlat === selectedFlat;
        });

        if (matchingUnit) {
          console.log('✅ Found matching unit:', matchingUnit);
          
          // Auto-select the unit
          setValue('unitNumber', matchingUnit.building_unit || matchingUnit.unit_number);
          setIsAutoFilled(true);
          
          // Show toast notification
          toast.success(`Unit ${matchingUnit.building_unit || matchingUnit.unit_number} automatically selected based on your building, floor, and flat selection.`);
        } else {
          console.log('❌ No matching unit found for:', {
            building: selectedBuilding,
            floor: selectedFloor,
            flat: selectedFlat
          });
          console.log('Available units for debugging:', units.map(unit => ({
            id: unit.id,
            unitNumber: unit.building_unit || unit.unit_number,
            building: unit.soc_building_name || unit.building_name,
            floor: unit.soc_building_floor?.toString() || unit.floor_number?.toString(),
            flat: unit.unit_flat_number
          })));
        }
      }, 100); // Small delay to ensure state stability

      return () => clearTimeout(timeoutId);
    }
  }, [selectedBuilding, selectedFloor, selectedFlat, buildings, flats, units, setValue, selectedUnitNumber]);

  // Reset auto-fill when building/floor/flat are cleared manually (but preserve when unit is selected)
  useEffect(() => {
    if ((!selectedBuilding || !selectedFloor || !selectedFlat) && isAutoFilled && !selectedUnitNumber) {
      console.log('🔄 Resetting auto-fill state because building/floor/flat were cleared');
      setIsAutoFilled(false);
    }
  }, [selectedBuilding, selectedFloor, selectedFlat, isAutoFilled, selectedUnitNumber]);

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);

    try {
      // Prepare the payload based on the schema requirements
      const payload: any = {};

      // If unit number is selected, use it directly
      if (data.unitNumber) {
        const selectedUnit = units.find(unit => 
          (unit.building_unit || unit.unit_number) === data.unitNumber
        );

        if (selectedUnit) {
          payload.fk_parking_unit_id = selectedUnit.id;
        }
      } else {
        // If building, floor, and flat are selected, use those
        if (data.building && data.floor && data.flat) {
          const selectedBuildingData = buildings.find(b => b.soc_building_name === data.building);
          const selectedFlatData = flats.find(f => f.unit_flat_number === data.flat);
          
          if (selectedBuildingData && selectedFlatData) {
            payload.soc_building_name = selectedBuildingData.id;
            payload.soc_building_floor = parseInt(data.floor);
            payload.flat = selectedFlatData.id;
          }
        }
      }

      console.log('Submitting vehicle registration:', payload);

      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}/admin/parking/registervehicle`,
        payload,
        {
          withCredentials: true,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data) {
        // Get success message from API response or use default
        const successMessage = response.data.message || 
                              response.data.success || 
                              'Vehicle registration completed successfully!';
        
        // Show success toast
        toast.success(successMessage);
        
        console.log('Vehicle registration successful:', response.data);
        
        // Reset form after successful submission
        reset();
        setIsAutoFilled(false);
        
        // Optionally redirect after a delay
        setTimeout(() => {
          window.location.href = '/admin/society/parking/list';
        }, 2000);
      }
    } catch (error: any) {
      // Get error message from API response or use default
      const errorMessage = error.response?.data?.message || 
                          error.response?.data?.error || 
                          error.response?.data?.details ||
                          'Failed to register vehicle. Please try again.';
      
      // Show error toast
      toast.error(errorMessage);
      
      console.error('Error submitting vehicle registration:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    reset();
    setIsAutoFilled(false);
  };

  const handleCancel = () => {
    // Navigate back logic
    window.history.back();
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header with Back button and Title */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <Typography variant="h6" component="h1">
          Unit Details
        </Typography>
      </Box>

      <Divider sx={{ mb: 3 }} />

      <Box>
        <form onSubmit={handleSubmit(onSubmit)}>
          {/* Unit Number */}
          <Grid container spacing={2} sx={{ mb: 3, alignItems: 'center' }}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Typography variant="body1">
                Unit Number <span style={{ color: 'red' }}>*</span>
              </Typography>
            </Grid>
            <Grid size={{ xs: 12, md: 8 }}>
              <Controller
                name="unitNumber"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>
                      {isAutoFilled ? 'Unit Number (Auto-selected)' : 'Unit Number'}
                    </InputLabel>
                    <Select
                      {...field}
                      label={isAutoFilled ? 'Unit Number (Auto-selected)' : 'Unit Number'}
                      IconComponent={KeyboardArrowDownIcon}
                      disabled={loadingUnits}
                      value={field.value || ''}
                      sx={isAutoFilled ? { 
                        '& .MuiSelect-select': { 
                          backgroundColor: '#e3f2fd',
                          fontWeight: 'bold'
                        }
                      } : {}}
                    >
                      {loadingUnits ? (
                        <MenuItem disabled>
                          <CircularProgress size={20} sx={{ mr: 1 }} />
                          Loading units...
                        </MenuItem>
                      ) : unitsError ? (
                        <MenuItem disabled sx={{ color: 'error.main' }}>
                          {unitsError}
                        </MenuItem>
                      ) : isAutoFilled ? (
                        [
                          <MenuItem key="auto-filled-info" disabled sx={{ color: 'info.main' }}>
                            Unit auto-selected from building, floor, and flat
                          </MenuItem>,
                          <MenuItem key="current-unit" value={field.value} sx={{ fontWeight: 'bold' }}>
                            {field.value}
                          </MenuItem>,
                          <MenuItem key="empty" value="">Clear selection</MenuItem>,
                          ...units.map((unit) => (
                            <MenuItem key={unit.id} value={unit.building_unit || unit.unit_number}>
                              {unit.building_unit || unit.unit_number}
                              {unit.building_name && ` - ${unit.building_name}`}
                            </MenuItem>
                          ))
                        ]
                      ) : [
                        <MenuItem key="empty" value="">Select Unit Number</MenuItem>,
                        ...units.map((unit) => (
                          <MenuItem key={unit.id} value={unit.building_unit || unit.unit_number}>
                            {unit.building_unit || unit.unit_number}
                            {unit.building_name && ` - ${unit.building_name}`}
                          </MenuItem>
                        )),
                        ...(units.length === 0 ? [
                          <MenuItem key="no-units" disabled>No units available</MenuItem>
                        ] : [])
                      ]}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
          </Grid>

          {/* OR separator */}
          <Box sx={{ textAlign: 'center', my: 2 }}>
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              OR
            </Typography>
          </Box>

          {/* Building(s) */}
          <Grid container spacing={2} sx={{ mb: 3, alignItems: 'center' }}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Typography variant="body1">
                Building(s)
              </Typography>
            </Grid>
            <Grid size={{ xs: 12, md: 8 }}>
              <Controller
                name="building"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <FormControl fullWidth>
                    <InputLabel>Building(s)</InputLabel>
                    <Select
                      {...field}
                      label="Building(s)"
                      IconComponent={KeyboardArrowDownIcon}
                      disabled={loadingBuildings || !!selectedUnitNumber}
                      value={field.value || ''}
                    >
                      {loadingBuildings ? (
                        <MenuItem disabled>
                          <CircularProgress size={20} sx={{ mr: 1 }} />
                          Loading buildings...
                        </MenuItem>
                      ) : buildingsError ? (
                        <MenuItem disabled sx={{ color: 'error.main' }}>
                          {buildingsError}
                        </MenuItem>
                      ) : selectedUnitNumber ? (
                        [
                          <MenuItem key="auto-filled" disabled sx={{ color: 'info.main' }}>
                            Building auto-filled from selected unit
                          </MenuItem>,
                          <MenuItem key="current-building" value={field.value} sx={{ fontWeight: 'bold' }}>
                            {field.value} 
                          </MenuItem>
                        ]
                      ) : [
                        <MenuItem key="empty" value="">Select Building</MenuItem>,
                        ...buildings.map((building) => (
                          <MenuItem key={building.id} value={building.soc_building_name}>
                            {building.soc_building_name}
                          </MenuItem>
                        )),
                        ...(buildings.length === 0 ? [
                          <MenuItem key="no-buildings" disabled>No buildings available</MenuItem>
                        ] : [])
                      ]}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
          </Grid>

          {/* Floor(s) */}
          <Grid container spacing={2} sx={{ mb: 3, alignItems: 'center' }}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Typography variant="body1">
                Floor(s) <span style={{ color: 'red' }}>*</span>
              </Typography>
            </Grid>
            <Grid size={{ xs: 12, md: 8 }}>
              <Controller
                name="floor"
                control={control}
                defaultValue=""
                render={({ field }) => {
                  const floorArray = getFloorArray();
                  const isDisabled = !selectedBuilding || floorArray.length === 0 || !!selectedUnitNumber;
                  
                  return (
                    <FormControl fullWidth>
                      <InputLabel>Floor(s) *</InputLabel>
                      <Select
                        {...field}
                        label="Floor(s) *"
                        IconComponent={KeyboardArrowDownIcon}
                        disabled={isDisabled}
                        value={field.value || ''}
                      >
                        {!selectedBuilding ? (
                          <MenuItem disabled>Select a building first</MenuItem>
                        ) : floorArray.length === 0 ? (
                          <MenuItem disabled>No floors available for this building</MenuItem>
                        ) : selectedUnitNumber ? (
                          [
                            <MenuItem key="auto-filled" disabled sx={{ color: 'info.main' }}>
                              Floor auto-filled from selected unit
                            </MenuItem>,
                            <MenuItem key="current-floor" value={field.value} sx={{ fontWeight: 'bold' }}>
                              Floor {field.value} 
                            </MenuItem>
                          ]
                        ) : [
                          <MenuItem key="empty" value="">Select Floor</MenuItem>,
                          ...floorArray.map((floor) => (
                            <MenuItem key={floor} value={floor.toString()}>
                              Floor {floor}
                            </MenuItem>
                          ))
                        ]}
                      </Select>
                    </FormControl>
                  );
                }}
              />
            </Grid>
          </Grid>

          {/* Flat(s) */}
          <Grid container spacing={2} sx={{ mb: 4, alignItems: 'center' }}>
            <Grid size={{ xs: 12, md: 4 }}>
              <Typography variant="body1">
                Flat(s) <span style={{ color: 'red' }}>*</span>
              </Typography>
            </Grid>
            <Grid size={{ xs: 12, md: 8 }}>
              <Controller
                name="flat"
                control={control}
                defaultValue=""
                render={({ field }) => {
                  const isDisabled = !selectedBuilding || !selectedFloor || loadingFlats || !!selectedUnitNumber;
                  const flatCount = flats.length;
                  
                  return (
                    <FormControl fullWidth>
                      <InputLabel>
                        {selectedUnitNumber ? 'Flat(s)' : (flatCount > 0 ? `Flat(s) (${flatCount} units available)` : 'Flat(s)')}
                      </InputLabel>
                      <Select
                        {...field}
                        label={selectedUnitNumber ? 'Flat(s)' : (flatCount > 0 ? `Flat(s) (${flatCount} units available)` : 'Flat(s)')}
                        IconComponent={KeyboardArrowDownIcon}
                        disabled={isDisabled}
                        value={field.value || ''}
                      >
                        {!selectedBuilding ? (
                          <MenuItem disabled>Select a building first</MenuItem>
                        ) : !selectedFloor ? (
                          <MenuItem disabled>Select a floor first</MenuItem>
                        ) : selectedUnitNumber ? (
                          [
                            <MenuItem key="auto-filled" disabled sx={{ color: 'info.main' }}>
                              Flat auto-filled from selected unit
                            </MenuItem>,
                            <MenuItem key="current-flat" value={field.value} sx={{ fontWeight: 'bold' }}>
                              {field.value} 
                            </MenuItem>
                          ]
                        ) : loadingFlats ? (
                          <MenuItem disabled>
                            <CircularProgress size={20} sx={{ mr: 1 }} />
                            Loading flats...
                          </MenuItem>
                        ) : flatsError ? (
                          <MenuItem disabled sx={{ color: 'error.main' }}>
                            {flatsError}
                          </MenuItem>
                        ) : [
                          <MenuItem key="empty" value="">
                            {flatCount > 0 ? `Select Flat (${flatCount} available)` : 'Select Flat'}
                          </MenuItem>,
                          ...flats.map((flat) => (
                            <MenuItem key={flat.id} value={flat.unit_flat_number}>
                              {flat.unit_flat_number}
                            </MenuItem>
                          )),
                          ...(flats.length === 0 ? [
                            <MenuItem key="no-flats" disabled>No flats available for this floor</MenuItem>
                          ] : [])
                        ]}
                      </Select>
                    </FormControl>
                  );
                }}
              />
            </Grid>
          </Grid>

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button
              type="submit"
              variant="contained"
              disabled={isSubmitting}
              sx={{ 
                bgcolor: '#4caf50',
                '&:hover': { bgcolor: '#45a049' },
                '&:disabled': { bgcolor: '#cccccc' }
              }}
            >
              {isSubmitting ? (
                <>
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
            
            <Button
              variant="outlined"
              onClick={handleReset}
              disabled={isSubmitting}
            >
              Reset
            </Button>
            
            <Button
              variant="outlined"
              onClick={handleCancel}
              disabled={isSubmitting}
              sx={{ 
                borderColor: '#f44336',
                color: '#f44336',
                '&:hover': { 
                  borderColor: '#d32f2f',
                  color: '#d32f2f'
                },
                '&:disabled': {
                  borderColor: '#cccccc',
                  color: '#cccccc'
                }
              }}
            >
              Cancel
            </Button>
          </Box>
        </form>
      </Box>
    </Box>
  );
};

export default VehicleRegistrationScreen;
