import { useState } from 'react';
import type { MouseEvent, ReactNode } from 'react';

import axios from 'axios';
import { Grid, Stack, Tooltip } from '@mui/material';
import { toast } from 'react-toastify';

import { replacePlaceholders } from '@/utils/replacePlaceholders';
import MenuIcon from './MenuAction';
import LinkAction from './LinkAction';
import type { Action, RowActionsProps } from '../../types/actions';
import StyledIconButton from './StyledIconButton';
import BulkEditAction from './BulkEditAction';
import { ellipsis, ensureAdminModeInPath } from '@/utils/string';
import ConfirmDialog from './ConfirmDialog';
import { determineFileType, getMimeTypeFromExtension } from '@/utils/url';
import { evaluateConditions } from '@/utils/conditions';

type RowActionParams = {
  event: MouseEvent;
  action?: Action;
  index?: number;
  row?: Record<string, any>;
  setLoadingActionIndex?: (index: number | null) => void;
  refetch?: () => void;
  push?: any
};

/**
   * Handles a click event and performs an API action (download, view, or default).
   * All original functionality is preserved.
   */
export const handleRowActionClick = async ({
  event,
  action,
  index,
  row,
  setLoadingActionIndex,
  refetch,
  push
}: RowActionParams): Promise<void> => {
  // Prevent event bubbling and default behavior
  event.stopPropagation();
  event.preventDefault();

  // Validate the action configuration
  if (!action?.api || typeof action.api.url !== 'string') {
    console.warn('Invalid action configuration:', action);
    toast.error('Invalid action configuration');

    return;
  }

  // Initialize a toast notification based on the API type
  let toastId: string | number | null = null;

  switch (action.api.type) {
    case 'download':
      toastId = toast.loading('Downloading...', {
        icon: <i className="line-md-downloading-loop" style={{ fontSize: '32px' }} />,
      });
      break;
    case 'view':
      toastId = toast.loading('Opening...', {
        icon: <i className="line-md-loading-loop" style={{ fontSize: '32px' }} />,
      });
      break;
    default:
      toastId = toast.loading('Loading...', {
        icon: <i className="line-md-loading-loop" style={{ fontSize: '32px' }} />,
      });
  }

  // Open a new tab if the action is for viewing a PDF
  let newTab: Window | null = null;

  if (action.api.type === 'view') {
    newTab = window.open(`${process.env.NEXT_PUBLIC_BASE_URL}/pdf.html`, '_blank');

    if (!newTab) {
      toast.update(toastId, {
        render: 'Please allow pop-ups for this website',
        type: 'error',
        isLoading: false,
        autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
      });

      return;
    }
  }

  // Set a loading indicator
  if (setLoadingActionIndex) setLoadingActionIndex?.(index ?? null);

  try {
    // Replace placeholders in the API URL with dynamic values
    const url = replacePlaceholders(action.api.url, row);

    const response = await axios(encodeURI(url), {
      method: action.api.method,
      baseURL: process.env.NEXT_PUBLIC_API_URL,
      validateStatus: () => true,
      withCredentials: true,
    });

    // Validate the API response
    if (!response?.data || (!response.data.isSuccess && response.data.status !== 'success')) {
      throw new Error(response?.data?.message || 'API request failed');
    }

    // For view or download actions, retrieve and process the PDF file.
    if (action?.api?.type === 'view' || action?.api?.type === 'download') {
      const fileUrl = response.data.data?.url;

      if (!fileUrl) {
        throw new Error('PDF URL is missing in the response');
      }

      // Fetch the PDF file as a blob.
      const fileResponse = await axios(fileUrl, {
        method: action.api.method,
        baseURL: process.env.NEXT_PUBLIC_API_URL,
        responseType: 'blob',
        validateStatus: () => true,
      });

      if (fileResponse.status !== 200) {
        throw new Error(fileResponse?.data?.message || `Failed to fetch file`);
      }

      const fileType = determineFileType(fileUrl, action.api.url);
      const mimeType = getMimeTypeFromExtension(fileType);

      // Create a Blob from the fetched file
      const fileBlob = new Blob([fileResponse.data], { type: mimeType });

      const fileURL = URL.createObjectURL(fileBlob);

      if (action.api.type === 'download') {
        // Create an anchor element to trigger the file download.
        const a = document.createElement('a');

        a.href = fileURL;
        a.download = `${document?.title || "Download"}.${fileType}`; // Use the determined file type for the download

        a.click();
        URL.revokeObjectURL(fileURL);
        toast.update(toastId, {
          render: 'Downloaded successfully',
          type: 'success',
          isLoading: false,
          autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
        });

        return;
      }

      // For viewing, embed the PDF in the new tab.
      if (newTab) {
        const pdfContainer = newTab.document.getElementById('pdfContainer');

        if (pdfContainer) {
          toast.update(toastId, {
            render: 'Loaded successfully',
            type: 'info',
            isLoading: false,
            autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
          });
          pdfContainer.innerHTML = `<object data="${fileURL}" type="application/pdf" style="width:100%; height:100%;"></object>`;
          const pdfObject = newTab.document.querySelector('object');

          if (pdfObject) {
            pdfObject.addEventListener('load', () => {
              const overlay = newTab!.document.getElementById('spinnerOverlay');

              if (overlay) {
                overlay.style.display = 'none';
              }

              newTab!.document.title = 'Your PDF is ready';
            });
          }
        }
      }
    } else {
      // console.log(action.api.method?.toLowerCase() === "delete" ? 'warning' : 'success', action?.api.method?.toLowerCase(), response.data.message);

      // For other API types, update the toast with the response message.
      toast.update(toastId, {
        render: response.data.message || 'Action completed successfully!',
        type: action.api.method?.toLowerCase() === "delete" ? 'warning' : 'success',
        isLoading: false,
        autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
      });
    }

    // console.log(action?.api?.redirect)

    if (action?.api?.redirect) {
      push(ensureAdminModeInPath(action.api?.redirect, action?.api?.mode || "society"))
    } else if (action.api.rerender || action.api.method) {
      refetch?.();
    }
  } catch (error: unknown) {
    let errorMessage = 'Something went wrong!';

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    // Update the toast with the error message.
    toast.update(toastId, {
      render: errorMessage,
      type: 'error',
      isLoading: false,
      autoClose: Number(process.env.NEXT_PUBLIC_TOAST_AUTO_CLOSE || 1000),
    });

    // If a new tab was opened, display an error page there.
    if (newTab) {
      newTab.document.open();
      newTab.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="UTF-8">
              <title>Error! Your PDF is not ready</title>
              <style>
                html, body { margin:0; padding:0; overflow:hidden; height:100%; width:100%; }
                #errorContainer {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  height: 100vh;
                  width: 100vw;
                  font-family: Arial, sans-serif;
                  background-color: #f8d7da;
                  text-align: center;
                }
              </style>
            </head>
            <body>
              <div id="errorContainer">
                <div style="font-size:20px; margin-bottom:20px; color:#721c24;">Error: ${errorMessage}</div>
                <div style="font-size:16px; color:#721c24;">This window will close in <span id="countdown">3</span> seconds.</div>
              </div>
              <script>
                'use strict';
                var seconds = 3;
                var countdownElement = document.getElementById('countdown');
                var interval = setInterval(function() {
                  seconds--;
                  countdownElement.textContent = seconds;
                  if (seconds <= 0) {
                    clearInterval(interval);
                    window.close();
                  }
                }, 1000);
              </script>
            </body>
          </html>
        `);
      newTab.document.close();
    }
  } finally {
    // Clear the loading indicator
    if (setLoadingActionIndex) setLoadingActionIndex?.(null);
  }
};

export const MAX_VISIBLE_BUTTONS = 6; // Maximum number of buttons to display in a single row`

const RenderRowActions: React.FC<RowActionsProps> = ({
  row,
  actions,
  setEditedRows,
  setIsEditing,
  table,
  refetch,
  push
}) => {
  const [loadingActionIndex, setLoadingActionIndex] = useState<number | null>(null);

  const [confirmState, setConfirmState] = useState<{
    open: boolean;
    action?: Action;
    index?: number;
    event?: MouseEvent;
  }>({ open: false });

  let direction: "row" | "column" = "row";

  const handleClick = (e: MouseEvent, action?: Action, index?: number) => {
    e.stopPropagation();
    e.preventDefault();

    // trigger confirm if action.confirm exists or if it's a DELETE API call
    if (action?.confirm || (action?.api?.method && action?.api.method?.toLowerCase() === 'delete')) {
      setConfirmState({ open: true, action, index, event: e });
    } else {
      handleRowActionClick({ event: e, action, index, row, setLoadingActionIndex, refetch, push });
    }
  };

  // Build an array of visible button components (filtering out hidden ones)
  const visibleButtons: ReactNode[] = actions
    .map((action, index) => {
      const {
        icon,
        title = '',
        color = 'primary',
        hide_on = {},
        disable_on = {},
        show_on = {},
        isadd,
        isdelete,
        isedit,
        shimmer = 'line-md-loading-loop',
        options = []
      } = action;

      // Check for hidden state first (early return if hidden)
      let hidden = evaluateConditions(row, hide_on);

      // show_on overrides hide_on
      if (show_on && Object.keys(show_on).length > 0) {
        const shouldShow = evaluateConditions(row, show_on);

        hidden = !shouldShow;
      }

      if (hidden) return null; // Early return - exactly like your original code

      // Only check disabled state if not hidden
      const disabled = evaluateConditions(row, disable_on);

      // Render based on the action type.
      if (isadd || isdelete || isedit) {
        return (
          <BulkEditAction
            key={index}
            action={action}
            table={table}
            index={index}
            row={row}
            setEditedRows={setEditedRows}
            setIsEditing={setIsEditing}
          />
        );
      } else if (options && Array.isArray(options) && options.length && icon) {
        return (
          <MenuIcon
            key={index}
            icon={icon}
            options={options}
            disabled={disabled}
            color={color}
            onClick={handleClick}
            index={index}
          />
        );
      } else if ((action?.href || action?.form) && !disabled) {
        return (
          <LinkAction
            key={index}
            action={action}
            row={row}
            index={index}
            loadingActionIndex={loadingActionIndex}
            disabled={disabled}
          />
        );
      } else if (action?.type === 'text') {
        direction = 'column';

        return (
          <Tooltip title={`${action?.title} ${row?.[action?.key]}`} disableInteractive key={index} >
            <span style={{ color: color }}>
              {ellipsis(`${action?.title} ${row?.[action?.key]}`, { maxLength: 15 })}
            </span>
          </Tooltip>
        );
      }

      return (
        <Tooltip title={title} disableInteractive key={index}>
          <span>
            <StyledIconButton
              size="small"
              disabled={disabled || loadingActionIndex === index}
              onClick={e => handleClick(e, action, index)}
              customColor={color}
            >
              <i className={loadingActionIndex === index ? shimmer : icon} />
            </StyledIconButton>
          </span>
        </Tooltip>
      );
    })
    .filter(Boolean);

  // Determine how to split the visible buttons into rows:
  // - 3 or fewer buttons: a single row.
  // - More than 3 buttons: split into two rows.
  const totalVisible = actions?.length;
  let rowsToRender: ReactNode[][] = [];

  if (totalVisible <= MAX_VISIBLE_BUTTONS) {
    rowsToRender = [visibleButtons];
  } else {
    const topRowCount = Math.ceil(totalVisible / 2);

    rowsToRender = [
      visibleButtons.slice(0, topRowCount),
      visibleButtons.slice(topRowCount)
    ];
  }

  const handleConfirm = () => {
    const { event, action, index } = confirmState;

    if (action && event) {
      handleRowActionClick({ event, action, index, row, setLoadingActionIndex, refetch });
    }

    setConfirmState({ open: false });
  };

  const handleCancel = () => setConfirmState({ open: false });

  // Render the rows using Grid for the layout
  return (
    <div>
      <ConfirmDialog
        open={confirmState.open}
        title={confirmState.action?.confirm?.title ?? confirmState.action?.title ?? 'Confirm action'}
        message={confirmState.action?.confirm?.message ?? 'Are you sure you want to proceed?'}
        confirmText={confirmState.action?.confirm?.confirmText ?? confirmState.action?.title ?? 'Confirm'}
        cancelText={confirmState.action?.confirm?.cancelText ?? 'Cancel'}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
      {rowsToRender.map((rowButtons, rowIndex) => {
        const isColumn = direction === 'column';

        if (isColumn) {
          return (
            <Stack spacing={1} key={`row-${rowIndex}`} direction="column">
              {rowButtons.map((button, btnIndex) => (
                <div key={`btn-${rowIndex}-${btnIndex}`}>{button}</div>
              ))}
            </Stack>
          );
        }

        return (
          <Grid container spacing={1} key={`row-${rowIndex}`}>
            {rowButtons.map((button, btnIndex) => (
              <Grid size={12 / rowButtons.length} key={`btn-${rowIndex}-${btnIndex}`}>
                {button}
              </Grid>
            ))}
          </Grid>
        );
      })}
    </div>
  );
};

export default RenderRowActions;
