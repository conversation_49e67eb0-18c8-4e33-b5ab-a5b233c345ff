'use client'

import React, { useState, useCallback, useEffect } from 'react'

import { usePathname, useRouter, useSearchParams } from 'next/navigation'

import { MaterialReactTable } from 'material-react-table'
import { Al<PERSON>, Box, lighten, Typography } from '@mui/material'
import { Icon } from '@iconify/react'

import type {
  MRT_ColumnFiltersState,
  MRT_PaginationState,
  MRT_RowSelectionState,
  MRT_SortingState
} from 'material-react-table'

import useTableSchema from '../hooks/useTableSchema'
import useTableHandlers, { type TableHandlers } from '../hooks/useTableHandlers'
import { getPerTableBindings, type PerTableBindings } from '../hooks/getPerTableBindings'
import TopToolbarCustomActions from '../TopToolbarCustomActions'
import RenderRowActions, { MAX_VISIBLE_BUTTONS } from '../actions/row/RowActions'
import type { ApiResponse } from '../types/tableTypes'
import { stripAdminScopedPath } from '@/utils/string'
import { Loader } from '@/components/Loader'

const isValidString = (value: any): boolean => {
  return typeof value === 'string' && value.trim() !== '';
};

const isValidNumber = (value: any): boolean => {
  const num = Number(value);

  return !isNaN(num) && num > 0;
};

const sanitizeString = (value: any): string => {
  if (typeof value !== 'string') return '';

  return value.replace(/[<>'"&]/g, '').substring(0, 1000);
};

const sanitizeFilterValue = (value: any): string => {
  if (value === null || value === undefined) return '';

  return sanitizeString(String(value));
};


interface RenderTableProps {
  isGate?: boolean
  customURL?: string | null
  rows?: ApiResponse['data']['data']
  schema?: ApiResponse['data']['meta'] | null
  onSelectionChange?: (selectedRows: any[]) => void
  handleSaveCell?: (cell: number | string, columnID: number | string, value: any) => void | null
  sectionName?: string
  hideTableIfEmpty?: boolean
  shouldAllowBack?: boolean
}

const RenderTable: React.FC<RenderTableProps> = ({
  isGate = false,
  customURL = null,
  rows = [],
  schema: defaultSchema = null,
  onSelectionChange,
  handleSaveCell = null,
  sectionName = '',
  hideTableIfEmpty = false,
  shouldAllowBack = false
}) => {
  // URL / router
  const pathName = usePathname()
  const asPath = stripAdminScopedPath(pathName)
  const searchParams = useSearchParams()
  const { replace, push } = useRouter()

  // Controlled state
  const [columnFilters, setColumnFilters] = useState<MRT_ColumnFiltersState>([])
  const [globalFilter, setGlobalFilter] = useState<string>('')

  const [extraFilters, setExtraFilters] = useState<Record<string, unknown>>(
    () => Object.fromEntries(searchParams?.entries()) || {}
  )

  const [sorting, setSorting] = useState<MRT_SortingState>([])

  const [pagination, setPagination] = useState<MRT_PaginationState>({
    pageIndex: 0,
    pageSize: 20
  })

  const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>(() => {
    if (!onSelectionChange) return {}

    return rows?.reduce((acc, row: any) => {
      acc[String(row.id)] = typeof row.selected === 'boolean' ? row.selected : true

      return acc
    }, {} as MRT_RowSelectionState)
  })

  const [isEditing, setIsEditing] = useState<boolean>(false)
  const [isSaving, setIsSaving] = useState<boolean>(false)

  // Tabs
  const [currentTab, setCurrentTab] = useState<string | null>(searchParams?.get('current_tab') || null)

  // 1. INITIALIZATION (runs once, reads URL → sets state)
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!searchParams || isInitialized) return;

    try {
      console.log('Initializing from URL params:', Object.fromEntries(searchParams.entries()));

      // Parse flat parameters (never put these in extraFilters)
      const urlTab = searchParams.get('current_tab');
      const urlPage = searchParams.get('page');
      const urlPerPage = searchParams.get('per_page');

      // Parse filters and sorts
      const filters: MRT_ColumnFiltersState = [];
      const sorts: MRT_SortingState = [];
      const extras: Record<string, any> = {};
      let urlSearch = '';

      searchParams.forEach((value, key) => {
        // Handle filters[key] format
        const filterMatch = key.match(/^filters\[([a-zA-Z_][a-zA-Z0-9_]*)\]$/);

        if (filterMatch) {
          const filterKey = filterMatch[1];
          const sanitizedValue = sanitizeFilterValue(value);

          if (filterKey === 'search') {
            urlSearch = sanitizedValue;
          } else {
            // ✅ Only put actual filter fields here (not meta parameters)
            filters.push({ id: filterKey, value: sanitizedValue });
            extras[filterKey] = sanitizedValue;
          }
        }

        // Handle sort[columnId] format
        const sortMatch = key.match(/^sort\[([a-zA-Z_][a-zA-Z0-9_]*)\]$/);

        if (sortMatch) {
          const columnId = sortMatch[1];
          const direction = sanitizeString(value).toLowerCase();

          if (direction === 'asc' || direction === 'desc') {
            sorts.push({ id: columnId, desc: direction === 'desc' });
          }
        }
      });

      // Set states with validation
      if (urlTab) setCurrentTab(sanitizeString(urlTab));
      if (urlSearch) setGlobalFilter(urlSearch);

      if (urlPage && isValidNumber(urlPage)) {
        const validPage = Math.max(1, Math.min(Number(urlPage), 1000));

        setPagination(prev => ({ ...prev, pageIndex: validPage - 1 }));
      }

      if (urlPerPage && isValidNumber(urlPerPage)) {
        const validPerPage = Math.max(1, Math.min(Number(urlPerPage), 100));

        setPagination(prev => ({ ...prev, pageSize: validPerPage }));
      }

      if (filters.length > 0) setColumnFilters(filters);
      if (sorts.length > 0) setSorting(sorts);

      // ✅ CRITICAL: Only set extraFilters, don't merge with existing
      setExtraFilters(extras); // Don't use prev => ({ ...prev, ...extras })

      console.log('Initialized states:', { urlTab, filters: filters.length, sorts: sorts.length, extras });

    } catch (error) {
      console.error('Error initializing from URL:', error);

      // Reset to clean defaults
      setCurrentTab(null);
      setGlobalFilter('');
      setColumnFilters([]);
      setSorting([]);
      setExtraFilters({}); // Clean reset
      setPagination({ pageIndex: 0, pageSize: 20 });
    } finally {
      setIsInitialized(true);
    }
  }, []); // Empty deps - runs once

  // **2. URL SYNC: State → URL (runs on state changes)**
  useEffect(() => {
    if (!isInitialized) return;

    try {
      const params = new URLSearchParams();

      // ✅ FLAT PARAMETERS (never nest these)
      if (currentTab && isValidString(currentTab)) {
        params.set('current_tab', sanitizeString(currentTab));
      }

      // ✅ PAGINATION - Always flat
      if (pagination?.pageIndex > 0) {
        params.set('page', String(pagination.pageIndex + 1));
      }

      if (pagination?.pageSize && pagination.pageSize !== 20) {
        params.set('per_page', String(pagination.pageSize));
      }

      // ✅ GLOBAL SEARCH under filters[]
      if (globalFilter && isValidString(globalFilter)) {
        params.set('filters[search]', sanitizeString(globalFilter));
      }

      // ✅ COLUMN FILTERS under filters[]
      columnFilters.forEach(({ id, value }) => {
        if (isValidString(id) && value !== undefined && value !== null && value !== '') {
          params.set(`filters[${sanitizeString(id)}]`, sanitizeFilterValue(value));
        }
      });

      // ✅ EXTRA FILTERS under filters[] (but exclude meta params)
      Object.entries(extraFilters).forEach(([key, val]) => {
        if (isValidString(key) && val !== undefined && val !== null && val !== '') {
          // ⚠️ CRITICAL: Don't re-add meta parameters
          const metaParams = ['current_tab', 'page', 'per_page', 'search'];

          if (!metaParams.includes(key)) {
            params.set(`filters[${sanitizeString(key)}]`, sanitizeFilterValue(val));
          }
        }
      });

      // ✅ SORTING under sort[]
      sorting.forEach(({ id, desc }) => {
        if (isValidString(id)) {
          params.set(`sort[${sanitizeString(id)}]`, desc ? 'desc' : 'asc');
        }
      });

      // Update URL
      const newSearch = params.toString();
      const newUrl = `${pathName}${newSearch ? `?${newSearch}` : ''}`;

      if (window.location.search !== `?${newSearch}` && newUrl.length <= 2000) {
        push(newUrl, { scroll: false });
      }

    } catch (error) {
      console.error('Error syncing state to URL:', error);
    }
  }, [
    isInitialized,
    columnFilters,
    sorting,
    globalFilter,
    pagination?.pageIndex,
    pagination?.pageSize,
    currentTab,
    extraFilters,
    pathName,
    push,
    pagination
  ]);


  // Query key
  const queryKey = [
    pagination,
    sorting,
    columnFilters,
    globalFilter,
    currentTab,
    extraFilters,
    asPath,
    defaultSchema,
    rows,
    customURL
  ] as const

  // Fetch schema & data
  const {
    data: schema,
    isRefetching,
    isPending,
    refetch,
    isError
  } = useTableSchema({
    customURL,
    isGate,
    currentTab,
    asPath,
    globalFilter,
    columnFilters,
    sorting,
    extraFilters,
    pagination,
    schema: defaultSchema,
    rows,
    handleSaveCell,
    queryKey,
    shouldAllowBack,
    push
  })

  // Save handlers hook
  const handlers: TableHandlers = useTableHandlers({
    queryKey,
    asPath,
    isGate,
    currentTab,
    customURL,
    refetch,
    schema: schema || null,
    setIsEditing
  })

  // Tab change
  const onTabChange = useCallback(
    (_: React.SyntheticEvent, newValue: string) => {
      const params = new URLSearchParams()

      params.set('current_tab', newValue)
      setCurrentTab(newValue)
      setColumnFilters([])
      setGlobalFilter('')
      setExtraFilters({})
      setSorting([])
      setPagination(p => ({ ...p, pageIndex: 0 }))
      replace(`?${params.toString()}`)
    },
    [replace]
  )

  if (isError) return <Alert severity='error'>{schema?.message || 'Error fetching data'} </Alert>

  if (!schema) return <Loader />

  const areRowsEmpty =
    !schema.rows ||
    schema.rows.every(arr => Array.isArray(arr) && (arr.length === 0 || (arr.length === 1 && arr[0]?.id === 'total')))

  if (hideTableIfEmpty && areRowsEmpty) {
    return null
  }

  const isReportsSection = sectionName?.toLowerCase() === "reports";

  return (
    <Box
      sx={{
        display: 'grid',
        gridTemplateColumns: {
          xs: 'auto',
          lg: schema.tableDirection === 'column' ? 'auto' : schema.columns.map(() => '1fr').join(' ')
        },
        gap: '1rem',
        overflow: 'auto'
      }}
    >
      {schema.columns.map((_, idx) => {
        // Bind per-table data + handlers via pure function
        const {
          rows: dataRows,
          editedRows,
          columns,
          setRows,
          updateEditedRows,
          handleSave,
          handleReset,
          handlePage,
          handleCell,
          handleRowDrag,
          handleEditRowSave
        }: PerTableBindings = getPerTableBindings(idx, schema, handlers, setRowSelection, setPagination, setIsSaving)

        const isColumnDirection = schema.tableDirection === "row";

        const isFirstTable = idx === 0;
        const isLastTable = idx === (schema?.columns?.length || 1) - 1;

        // only show pagination if it's the last table and isColumnDirection is true, if it it report then show pagination on first table
        const showPagination = isReportsSection
          ? isFirstTable
          : (isColumnDirection ? isLastTable : isFirstTable)

        return (
          <MaterialReactTable
            key={idx}
            data={schema.isEditable && schema.editMode !== 'row' ? editedRows : dataRows}
            columns={columns}
            displayColumnDefOptions={{
              'mrt-row-select': { size: 30, grow: false },
              'mrt-row-expand': { size: 50, grow: true },
              'mrt-row-actions': {
                grow: false,
                minSize: 60,
                size: schema?.rowActions?.[idx]?.length
                  ? schema?.rowActions?.[idx]?.length <= MAX_VISIBLE_BUTTONS
                    ? schema?.rowActions?.[idx]?.length * 35
                    : Math.ceil(schema?.rowActions?.[idx]?.length / 2) * 35
                  : 0
              }
            }}
            getRowId={r => r?.id}
            enableEditing={schema.isEditable}
            editDisplayMode={schema.editMode}
            createDisplayMode='row'

            // Cell edits
            onEditingCellChange={handleCell}
            onEditingRowSave={(...params)=>handleEditRowSave(...params, schema?.rowActions)}

            // muiEditTextFieldProps={({cell}): any => ({
            //   variant: 'outlined',
            //   size: 'small',
            //   fullWidth: true,
            // })}

            // Row ordering & dragging
            enableRowOrdering={!!schema.orderable}
            autoResetPageIndex={!!schema.orderable}
            muiRowDragHandleProps={meta => {
              const tableInst = meta?.table

              if (!tableInst) return {}

              return {
                onDragEnd: () => {
                  const { draggingRow, hoveredRow } = tableInst.getState()

                  if (draggingRow && hoveredRow) {
                    handleRowDrag(draggingRow, hoveredRow, schema.orderable)
                  }
                }
              }
            }}

            // Pagination / Filtering / Sorting
            manualPagination
            manualFiltering
            filterFromLeafRows
            paginationDisplayMode='pages'
            onPaginationChange={handlePage}
            enablePagination={showPagination}
            muiPaginationProps={{
              variant: 'outlined',
              color: 'primary',
              showRowsPerPage: true,
              rowsPerPageOptions: [5, 10, 20, 50],
              SelectProps: {
                sx: {
                  '& .MuiSelect-select': {
                    paddingRight: '40px !important',
                  },
                },
              },
            }}
            onSortingChange={setSorting}
            onGlobalFilterChange={setGlobalFilter}
            onColumnFiltersChange={f => {
              setPagination(p => ({ ...p, pageIndex: 0 }))
              setColumnFilters(f)
            }}

            // Table state
            state={{
              isSaving,
              isLoading: isPending,
              pagination,
              columnFilters,
              globalFilter,
              sorting,
              rowSelection,
              showProgressBars: isRefetching,
              columnOrder: [
                'mrt-row-numbers',
                'mrt-row-select',
                'mrt-row-expand',
                'mrt-row-pin',
                'mrt-row-drag',
                ...(columns?.map(col => col?.id || col?.accessorKey) || []),
                'mrt-row-actions'
              ]
            }}
            initialState={{
              density: 'compact',
              showGlobalFilter: true,
              showColumnFilters: false,
              expanded: true

              // columnPinning: {
              //   right: ['mrt-row-actions']
              // }
            }}

            // Full toolbar override
            renderTopToolbar={({ table }) => (
              <TopToolbarCustomActions
                table={table}
                tableIndex={idx}
                schema={schema}
                setRows={setRows}
                setEditedRows={updateEditedRows}
                setIsEditing={setIsEditing}
                handleSaveChanges={handleSave}
                handleResetChanges={handleReset}
                rowSelection={rowSelection}
                isEditing={isEditing}
                loading={isPending || isRefetching || isSaving}
                refetch={refetch}
                isGate={isGate}
                extraFilters={extraFilters}
                setExtraFilters={setExtraFilters}
                handleTabChange={onTabChange}
                currentTab={currentTab}
                handleResetFilters={() => {
                  setColumnFilters([])
                  setGlobalFilter('')
                  setExtraFilters({})
                  setSorting([])
                  setPagination(p => ({ ...p, pageIndex: 0 }))
                }}
                columnFilters={columnFilters}
                pathname={pathName}
                shouldHideSave={!!handleSaveCell}
                sectionName={sectionName}
              />
            )}

            // Row actions
            enableRowActions={!!schema.rowActions?.[idx]?.length}
            renderRowActions={({ row, table }) => (
              <RenderRowActions
                row={row.original}
                actions={schema.rowActions?.[idx] || []}
                setEditedRows={updateEditedRows}
                setIsEditing={setIsEditing}
                table={table}
                refetch={refetch}
                push={push}
              />
            )}

            // Selection
            enableRowSelection={schema.multiSelect}
            {...(schema?.enableSelectOn && {
              enableRowSelection: (row: any): boolean =>
                row?.original?.[schema?.enableSelectOn?.key] === schema?.enableSelectOn?.value
            })}
            onRowSelectionChange={u => {
              const sel = typeof u === 'function' ? u(rowSelection) : u

              setRowSelection(sel)
              onSelectionChange?.(Object.keys(sel))
            }}
            enableExpanding={schema?.rows?.[idx]?.some((row: any) => row?.rows || row?.children)}
            getSubRows={(row: any) => row?.rows || row?.children || []}

            // Misc
            rowCount={schema?.total || 0}
            manualSorting
            muiTableBodyProps={{
              sx: theme => ({
                '& tr:nth-of-type(even) > td': {
                  backgroundColor: lighten(theme.palette.primary.main, 0.96)
                },
                '& tr:nth-of-type(odd) > td': {
                  backgroundColor: lighten(theme.palette.background.paper, 0.04)
                },
                '& tr:hover > td': {
                  backgroundColor: lighten(theme.palette.primary.main, 0.9)
                }
              })
            }}
            muiTableContainerProps={{
              sx: {
                ...(schema.tableDirection !== 'column' && schema?.columns?.length === 2
                  ? { height: 'calc(100vh - 270px)' }
                  : { maxHeight: 'calc(100vh - 270px)' })

                // overflowY: 'auto',
                // overflowX: 'auto',
              }
            }}

            // enableBottomToolbar={
            //   schema.totalTables > 1
            //     ? idx + 1 === schema.totalTables
            //     : true
            // }
            enableStickyFooter
            enableStickyHeader
            layoutMode='grid'
          />
        )
      })}

      {/* Help Note Section */}
      {schema?.helpNote && Array.isArray(schema.helpNote) && schema.helpNote.length > 0 && (
        <Box
          sx={{
            gridColumn: '1 / -1',
            mt: 2,
            p: 2,
            backgroundColor: 'background.paper',
            borderRadius: 1,
            border: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Typography
            variant='h6'
            sx={{
              fontWeight: 600,
              mb: 1,
              color: 'text.primary'
            }}
          >
            Help Note
          </Typography>
          <Box component='ol' sx={{ pl: 2, m: 0 }}>
            {schema.helpNote.map((note: string, index: number) => {
              // Replace icon class names with actual icons
              const iconRegex = /(ri-[a-z0-9-]+(?:-line|-fill)?)/gi
              const parts = note.split(iconRegex)

              return (
                <Typography
                  key={index}
                  component='li'
                  variant='body2'
                  sx={{
                    color: 'text.secondary',
                    mb: 0.5,
                    lineHeight: 1.5,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  {parts.map((part, i) => {
                    if (iconRegex.test(part)) {
                      return (
                        <span key={i} style={{ display: 'inline-flex', alignItems: 'center', marginRight: 4 }}>
                          <Icon icon={part} style={{ fontSize: 18, verticalAlign: 'middle', color: '#1976d2' }} />
                        </span>
                      )
                    }

                    return <span key={i}>{part}</span>
                  })}
                </Typography>
              )
            })}
          </Box>
        </Box>
      )}
    </Box>
  )
}

export default RenderTable
