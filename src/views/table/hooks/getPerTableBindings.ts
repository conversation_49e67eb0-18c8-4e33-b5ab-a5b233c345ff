/**
 * getPerTableBindings.ts
 *
 * A pure function (not a hook) that, given:
 *  • a tableIndex,
 *  • the fetched multi-table schema,
 *  • the shared handlers from useTableHandlers,
 *  • rowSelection & pagination setters,
 * returns the per-table slice of:
 *  • rows / editedRows / columns
 *  • bound handler functions for that index
 */

import type {
  LiteralUnion,
  MRT_PaginationState,
  MRT_Row,
  MRT_RowSelectionState,
  MRT_TableInstance,
} from 'material-react-table';

import type { TableSchema } from '../types/tableTypes';
import type { TableHandlers } from './useTableHandlers';

export interface PerTableBindings {
  rows: unknown[];
  editedRows: unknown[];
  columns: unknown[];
  setRows: (v: unknown) => void;
  updateEditedRows: (v: unknown) => void;
  handleSave: () => void;
  handleReset: () => void;
  handlePage: (updaterOrValue: unknown) => void;
  handleCell: (id: unknown, field: string, value: unknown) => void;
  handleRowDrag: (
    draggingRow: { original: { id: unknown } },
    hoveredRow: { original: { id: unknown } },
    orderableField: string
  ) => void;
  handleEditRowSave: (props: { exitEditingMode: () => void; row: MRT_Row<unknown>; table: MRT_TableInstance<unknown>; values: Record<LiteralUnion<string, string>, any>; }) => void
}

export function getPerTableBindings(
  tableIndex: number,
  schema: TableSchema,
  handlers: TableHandlers,
  setRowSelection: React.Dispatch<React.SetStateAction<MRT_RowSelectionState>>,
  setPagination: React.Dispatch<React.SetStateAction<MRT_PaginationState>>,
  setIsSaving: React.Dispatch<React.SetStateAction<boolean>>,
): PerTableBindings {
  const rows = schema.rows?.[tableIndex] || [];
  const editedRows = schema.editedRows?.[tableIndex] || [];
  const columns = schema.columns?.[tableIndex] || [];

  return {
    rows,
    editedRows,
    columns,
    setRows: (v) => handlers.setRows(tableIndex, v),
    updateEditedRows: (v) => handlers.updateEditedRows(tableIndex, v),
    handleSave: () => handlers.handleSaveChanges(tableIndex),
    handleReset: () => handlers.handleResetChanges(tableIndex),
    handlePage: (u) =>
      handlers.handlePaginationChange(
        tableIndex,
        u,
        setRowSelection,
        setPagination
      ),
    handleCell: (id, field, value) =>
      handlers.handleCellChange(id, field, value, tableIndex),
    handleRowDrag: (draggingRow, hoveredRow, field) =>
      handlers.handleRowDragEnd(
        tableIndex,
        tableIndex,
        draggingRow,
        hoveredRow,
        field
      ),
    handleEditRowSave: ({ row, values, exitEditingMode }, actions) => {
      if (schema?.editMode !== 'table') {
        return handlers.handleEditRowSave({ row, values, exitEditingMode }, schema?.updatePath, setIsSaving, actions);
      }
    },
  };
}
