import { useCallback } from 'react';

import type { Dispatch, SetStateAction } from 'react';

import axios from 'axios';
import { toast } from 'react-toastify';

import type {
  MRT_PaginationState,
  MRT_RowSelectionState,
} from 'material-react-table';

import { queryClient } from '@/contexts/queryClientProvider';
import type { TableSchema } from '../types/tableTypes';
import { replacePlaceholders } from '@/utils/replacePlaceholders';

export default function useTableHandlers({
  queryKey,
  asPath,
  isGate,
  currentTab,
  customURL,
  refetch,
  schema,
  setIsEditing,
}: {
  queryKey: unknown[];
  asPath: string | null;
  isGate: boolean;
  currentTab: string | null;
  customURL?: string | null;
  refetch: () => void;
  schema?: TableSchema | null;
  setIsEditing: (isEditing: boolean) => void;
}) {
  const setRows = useCallback(
    (tableIndex: number, newValueOrUpdater: unknown) => {
      queryClient.setQueryData(queryKey, (prev: any) => {
        if (!prev) return prev;
        const arr: any[][] = prev.rows || [];
        const current = arr[tableIndex] || [];

        const updated =
          typeof newValueOrUpdater === 'function'
            ? (newValueOrUpdater as any)(current)
            : newValueOrUpdater;

        const newArr = [...arr];

        newArr[tableIndex] = updated;

        return { ...prev, rows: newArr };
      });
    },
    [queryKey]
  );

  const updateEditedRows = useCallback(
  (tableIndex: number, newValueOrUpdater: unknown) => {
    queryClient.setQueryData(queryKey, (prev: any) => {
      if (!prev) {
        return prev;
      }

      const arr: any[][] = (prev?.editMode === "row" ? prev.rows : prev.editedRows) || [];
      const current = arr[tableIndex];

      const updated =
        typeof newValueOrUpdater === 'function'
          ? (newValueOrUpdater as (curr: any) => any)(current)
          : newValueOrUpdater;

      const newArr = [...arr];

      newArr[tableIndex] = updated;
      const newData = { ...prev, editedRows: newArr };

      return newData;
    });
  },
  [queryKey]
);

  const handleSaveChanges = useCallback(
    async (tableIndex: number) => {
      if (!schema) return;

      try {
        const edited = schema.editedRows[tableIndex] || [];
        const original = schema.rows[tableIndex] || [];

        // 1) Check for client-side errors
        if (
          edited.some(
            (r: any) =>
              r._error && Object.values(r._error).some((msg: string) => Boolean(msg))
          )
        ) {
          toast.error('Please resolve all errors before saving changes.');

          return;
        }

        // 2) Build diff
        const updatedParams = edited
          .map((r: any) => {
            const orig = original.find((o: any) => o.id === r.id);

            if (!orig) return r;

            const hasChanges = Object.keys(r).some((k) =>
              k === '_action' && r[k] === 'edit' ? false : r[k] !== orig[k]
            );

            return hasChanges ? r : null;
          })
          .filter((r) => r !== null);

        if (!updatedParams.length) {
          toast.info('No changes to save.');
          updateEditedRows(tableIndex, (prev: any[]) =>
            prev.map((r) => ({ ...r, _action: null }))
          );

          return;
        }

        // 3) Send to backend
        const response = await axios.post(
          schema.bulkUpdatePath || customURL || asPath || '',
          {
            params: schema.sendFullParams ? edited : updatedParams,
          },
          {
            baseURL: isGate ? '/api' : process.env.NEXT_PUBLIC_API_URL,
            params: { current_tab: currentTab },
            validateStatus: () => true,
            withCredentials: true,
          }
        );

        // 4) Clear local errors
        updatedParams.forEach((r: any) => delete r._error);

        // 5) Merge server errors
        const errors = response.data.meta?.errors || {};

        if (Object.keys(errors).length) {
          Object.entries(errors).forEach(([key, val]) => {
            if (key.startsWith('params.')) {
              const [, idx, field] = key.split('.');
              const row = updatedParams[Number(idx)];

              if (row) {
                row._error = {
                  ...row._error,
                  [field]: Array.isArray(val) ? val.join(', ') : val,
                };
              }
            } else {
              const idx = Number(key);

              if (updatedParams[idx]) {
                updatedParams[idx]._error = val;
              }
            }
          });
          updateEditedRows(tableIndex, (prev: any[]) =>
            prev.map((r) => {
              const match = updatedParams.find((p: any) => p.id === r.id);

              if (!match) {
                delete r._error;
                delete r._action;

                return r;
              }

              return { ...r, _error: { ...r._error, ...match._error } };
            })
          );
        }

        // 6) Toast backend messages
        const msgs = response.data.message;

        if (Array.isArray(msgs)) {
          msgs.forEach((m: string) =>
            toast(m, {
              type: response.data.status === 'success' ? 'success' : 'warning',
            })
          );
        } else if (typeof msgs === 'string') {
          toast(msgs, {
            type: response.data.status === 'success' ? 'success' : 'warning',
          });
        }

        // 7) On success, exit edit mode and refetch
        if (response.data.status === 'success') {
          setIsEditing(false);
          refetch();
        }
      } catch (error: any) {
        console.warn('Error saving changes:', error);
        toast.error('Failed to save changes. ' + error.message);
      }
    },
    [
      schema,
      customURL,
      asPath,
      currentTab,
      isGate,
      refetch,
      updateEditedRows,
      setIsEditing,
    ]
  );

  const handleResetChanges = useCallback(
    (tableIndex: number) => {
      if (!schema) return;
      updateEditedRows(tableIndex, schema.rows[tableIndex]);
      setIsEditing(false);
      toast.info('Changes have been reset.');
    },
    [schema, updateEditedRows, setIsEditing]
  );

  const handlePaginationChange = useCallback(
    (
      tableIndex: number,
      updaterOrValue: unknown,
      setRowSelection: Dispatch<SetStateAction<MRT_RowSelectionState>>,
      setPagination: Dispatch<SetStateAction<MRT_PaginationState>>
    ) => {
      setRowSelection(() => ({}));
      setPagination((old) =>
        typeof updaterOrValue === 'function'
          ? (updaterOrValue as any)(old)
          : (updaterOrValue as any)
      );
    },
    []
  );

  const handleCellChange = useCallback(
    (id: unknown, field: string, value: unknown, tableIndex = 0) => {      
      if (!id || !field) return;
      updateEditedRows(tableIndex, (prev: any[]) => {
        const copy = [...prev];
        const idx = copy.findIndex((r) => r.id === id);

        if (idx === -1) return prev;
        copy[idx] = { ...copy[idx], [field]: value, _action: copy[idx]?.["_action"] === "add" ? "add" : 'edit' };

        return copy;
      });

      // Set isEditing to true when a cell is changed
      setIsEditing(true);
    },
    [updateEditedRows, setIsEditing]
  );

  const handleRowDragEnd = useCallback(
    (
      sourceIndex: number,
      destIndex: number,
      draggingRow: { original: { id: unknown } },
      hoveredRow: { original: { id: unknown } },
      orderableField: string
    ) => {
      setIsEditing(true);

      // same-table reorder

      if (sourceIndex === destIndex) {
        updateEditedRows(sourceIndex, (prev: any[]) => {
          const arr = [...prev];
          const from = arr.findIndex((r) => r.id === draggingRow.original.id);
          const to = arr.findIndex((r) => r.id === hoveredRow.original.id);

          if (from < 0 || to < 0) return prev;
          const [moved] = arr.splice(from, 1);

          arr.splice(to, 0, moved);

          return arr.map((r, i) => ({ ...r, [orderableField]: i + 1 }));
        });

        return;
      }

      // cross-table move
      queryClient.setQueryData(queryKey, (prev: any) => {
        const src = [...(prev.editedRows[sourceIndex] || [])];
        const dst = [...(prev.editedRows[destIndex] || [])];
        const from = src.findIndex((r) => r.id === draggingRow.original.id);

        if (from < 0) return prev;
        const [moved] = src.splice(from, 1);
        const to = dst.findIndex((r) => r.id === hoveredRow.original.id);

        dst.splice(to >= 0 ? to : dst.length, 0, moved);
        const newEdited = [...prev.editedRows];

        newEdited[sourceIndex] = src;
        newEdited[destIndex] = dst;

        return { ...prev, editedRows: newEdited };
      });
    },
    [updateEditedRows, setIsEditing, queryKey]
  );

  const handleEditRowSave = useCallback(
    ({ row, values, exitEditingMode}, updatePath ="", setIsSaving, actions = []) => {
        (async (): Promise<void> => {
          const editAction = new Array(actions).flat(3).find((a: any) => a?.isedit);

          try {
            setIsSaving(true);

            const url = updatePath || customURL || asPath;
            const URL = editAction?.api?.url || `${url}/${values?.actions === 'new' ? '' : row?.id}`;
            const parsedURL = replacePlaceholders(URL, row);

            const response = await axios(parsedURL, {
              baseURL: process.env.NEXT_PUBLIC_API_URL,
              method: editAction?.api?.method || (values?.actions === 'new' ? 'post' : 'patch'),
              data: values,
              validateStatus: (): boolean => true,
              withCredentials: true
            });

            if (response?.data?.status_code !== 200) {
              throw new Error(
                response?.data?.message ||
                'Failed to save changes. Please try again later.'
              );
            }

            toast.success(
              response?.data?.message || 'Changes saved successfully!'
            );
            exitEditingMode();
            refetch();
          } catch (error) {
            console.error('Error saving changes:', error);
            toast.error(error?.message || 'Failed to save changes. Please try again later.');
          } finally {
            setIsSaving(false);
          }
        })();
    },
    [customURL, asPath, refetch]
  );

  return {
    setRows,
    updateEditedRows,
    handleSaveChanges,
    handleResetChanges,
    handlePaginationChange,
    handleCellChange,
    handleRowDragEnd,
    handleEditRowSave,
  };
}

/**
 * Extractable type for the handlers this hook returns
 */
export type TableHandlers = ReturnType<typeof useTableHandlers>;
