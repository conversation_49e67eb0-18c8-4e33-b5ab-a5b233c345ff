/* ------------------------------------------------------------------
   evaluateConditions.spec.ts
   ------------------------------------------------------------------ */

import { type ComparisonOperator, evaluateConditions, type LogicOperator } from "../../src/utils/conditions";

/* Helper to create conditions */
const C = (
  key: string | null,
  operator: ComparisonOperator,
  value: unknown,
) => ({ key, operator, value });

describe('evaluateConditions - exhaustive test cases', () => {

  /* ================================================================
     SHARED TEST ROW - covers all data types
     =============================================================== */
  const ROW = {
    user_id: null,                    // null value
    status: 1,                        // number (integer)
    member_type_name: 'Nominal',      // string
    score: 85.5,                      // number (float)
    tags: 'admin,staff',              // string (comma-separated)
    email: '<EMAIL>',        // string with special chars
    level: 7,                         // number
    disable: 2,                       // number
    age: 30,                          // number
    active: true,                     // boolean
    member_id: undefined,             // undefined value
    permissions: ['read', 'write'],   // array
    settings: { theme: 'dark' },      // object
    count: 0,                         // zero
    balance: -50.25,                  // negative number
    name: '',                         // empty string
  };

  /* ================================================================
     1. SIMPLE EQUALITY TESTS
     =============================================================== */
  describe('Simple equality conditions', () => {
    it('should match string equality', () => {
      expect(evaluateConditions(ROW, { member_type_name: 'Nominal' })).toBe(true);
      expect(evaluateConditions(ROW, { member_type_name: 'Associate' })).toBe(false);
    });

    it('should match number equality', () => {
      expect(evaluateConditions(ROW, { status: 1 })).toBe(true);
      expect(evaluateConditions(ROW, { status: 2 })).toBe(false);
    });

    it('should match boolean equality', () => {
      expect(evaluateConditions(ROW, { active: true })).toBe(true);
      expect(evaluateConditions(ROW, { active: false })).toBe(false);
    });

    it('should match zero and negative numbers', () => {
      expect(evaluateConditions(ROW, { count: 0 })).toBe(true);
      expect(evaluateConditions(ROW, { balance: -50.25 })).toBe(true);
    });

    it('should match empty string', () => {
      expect(evaluateConditions(ROW, { name: '' })).toBe(true);
    });
  });

  /* ================================================================
     2. NULL / UNDEFINED HANDLING
     =============================================================== */
  describe('Null and undefined handling', () => {
    it('should treat null and undefined as equal', () => {
      expect(evaluateConditions(ROW, { user_id: null })).toBe(true);
      expect(evaluateConditions(ROW, { member_id: null })).toBe(true);
    });

    it('should handle explicit null/undefined conditions', () => {
      expect(evaluateConditions(ROW, { user_id: { operator: 'equal', value: null } })).toBe(true);
      expect(evaluateConditions(ROW, { member_id: { operator: 'equal', value: null } })).toBe(true);
    });

    it('should handle notEqual with null', () => {
      expect(evaluateConditions(ROW, { status: { operator: 'notEqual', value: null } })).toBe(true);
      expect(evaluateConditions(ROW, { user_id: { operator: 'notEqual', value: null } })).toBe(false);
    });
  });

  /* ================================================================
     3. ARRAY INCLUSION (FLAT KEYED OBJECT)
     =============================================================== */
  describe('Array inclusion conditions', () => {
    it('should match when value is in array', () => {
      expect(evaluateConditions(ROW, { status: [1, 2, 3] })).toBe(true);
      expect(evaluateConditions(ROW, { member_type_name: ['Nominal', 'Associate'] })).toBe(true);
    });

    it('should fail when value is not in array', () => {
      expect(evaluateConditions(ROW, { status: [2, 3, 4] })).toBe(false);
      expect(evaluateConditions(ROW, { member_type_name: ['Primary', 'Associate'] })).toBe(false);
    });

    it('should handle mixed types in arrays', () => {
      expect(evaluateConditions(ROW, { status: [1, 'active', true] })).toBe(true);
      expect(evaluateConditions(ROW, { active: [1, 'active', true] })).toBe(true);
    });
  });

  /* ================================================================
     4. NUMERIC COMPARISON OPERATORS
     =============================================================== */
  describe('Numeric comparison operators', () => {
    it('should handle greaterThan', () => {
      expect(evaluateConditions(ROW, { age: { operator: 'greaterThan', value: 25 } })).toBe(true);
      expect(evaluateConditions(ROW, { age: { operator: 'greaterThan', value: 35 } })).toBe(false);
      expect(evaluateConditions(ROW, { score: { operator: 'greaterThan', value: 85 } })).toBe(true);
    });

    it('should handle lessThan', () => {
      expect(evaluateConditions(ROW, { age: { operator: 'lessThan', value: 35 } })).toBe(true);
      expect(evaluateConditions(ROW, { age: { operator: 'lessThan', value: 25 } })).toBe(false);
      expect(evaluateConditions(ROW, { balance: { operator: 'lessThan', value: 0 } })).toBe(true);
    });

    it('should handle greaterThanOrEqual', () => {
      expect(evaluateConditions(ROW, { age: { operator: 'greaterThanOrEqual', value: 30 } })).toBe(true);
      expect(evaluateConditions(ROW, { age: { operator: 'greaterThanOrEqual', value: 31 } })).toBe(false);
    });

    it('should handle lessThanOrEqual', () => {
      expect(evaluateConditions(ROW, { age: { operator: 'lessThanOrEqual', value: 30 } })).toBe(true);
      expect(evaluateConditions(ROW, { age: { operator: 'lessThanOrEqual', value: 29 } })).toBe(false);
    });

    it('should fail numeric comparisons on non-numbers', () => {
      expect(evaluateConditions(ROW, { email: { operator: 'greaterThan', value: 10 } })).toBe(false);
      expect(evaluateConditions(ROW, { member_type_name: { operator: 'lessThan', value: 'Z' } })).toBe(false);
    });
  });

  /* ================================================================
     5. STRING OPERATORS
     =============================================================== */
  describe('String operators', () => {
    it('should handle contains operator', () => {
      expect(evaluateConditions(ROW, { email: { operator: 'contains', value: '@example.com' } })).toBe(true);
      expect(evaluateConditions(ROW, { tags: { operator: 'contains', value: 'admin' } })).toBe(true);
      expect(evaluateConditions(ROW, { email: { operator: 'contains', value: '@gmail.com' } })).toBe(false);
    });

    it('should fail contains on non-strings', () => {
      expect(evaluateConditions(ROW, { age: { operator: 'contains', value: '3' } })).toBe(false);
      expect(evaluateConditions(ROW, { active: { operator: 'contains', value: 'true' } })).toBe(false);
    });

    it('should handle in operator', () => {
      expect(evaluateConditions(ROW, { member_type_name: { operator: 'in', value: ['Nominal', 'Primary'] } })).toBe(true);
      expect(evaluateConditions(ROW, { status: { operator: 'in', value: [1, 2, 3] } })).toBe(true);
      expect(evaluateConditions(ROW, { level: { operator: 'in', value: [5, 6] } })).toBe(false);
    });
  });

  /* ================================================================
     6. LOGICAL CONDITIONS (ANY/ALL)
     =============================================================== */
  describe('Logical conditions', () => {
    it('should handle logic:any (default) - passes when any condition is true', () => {
      const cond = {
        conditions: [
          C('status', 'equal', 999),    // false
          C('level', 'equal', 7),       // true
        ],
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });

    it('should handle logic:any explicitly', () => {
      const cond = {
        logic: 'any' as LogicOperator,
        conditions: [
          C('user_id', 'equal', 'not-null'),  // false
          C('status', 'equal', 1),            // true
        ],
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });

    it('should handle logic:all - passes only when all conditions are true', () => {
      const cond = {
        logic: 'all' as LogicOperator,
        conditions: [
          C('status', 'equal', 1),           // true
          C('level', 'greaterThan', 5),      // true
          C('active', 'equal', true),        // true
        ],
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });

    it('should fail logic:all when any condition fails', () => {
      const cond = {
        logic: 'all' as LogicOperator,
        conditions: [
          C('status', 'equal', 1),           // true
          C('level', 'equal', 999),          // false
        ],
      };

      expect(evaluateConditions(ROW, cond)).toBe(false);
    });
  });

  /* ================================================================
     7. NESTED LOGICAL CONDITIONS
     =============================================================== */
  describe('Nested logical conditions', () => {
    it('should handle nested any/all combinations', () => {
      const cond = {
        logic: 'all' as LogicOperator,
        conditions: [
          C('age', 'greaterThan', 18),
          {
            logic: 'any' as LogicOperator,
            conditions: [
              C('level', 'equal', 5),
              C('level', 'equal', 7),        // this will match
            ],
          },
        ],
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });

    it('should handle deeply nested conditions', () => {
      const cond = {
        logic: 'all' as LogicOperator,
        conditions: [
          C('status', 'equal', 1),
          {
            logic: 'any' as LogicOperator,
            conditions: [
              {
                logic: 'all' as LogicOperator,
                conditions: [
                  C('age', 'greaterThan', 25),
                  C('score', 'greaterThan', 80),
                ],
              },
              C('level', 'equal', 999),      // fallback that won't match
            ],
          },
        ],
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });
  });

  /* ================================================================
     8. LEGACY WRAPPER KEYS (disable, hide, show)
     =============================================================== */
  describe('Legacy wrapper keys', () => {
    it('should handle disable array format', () => {
      expect(evaluateConditions(ROW, { disable: [1, 2, 3] })).toBe(true);
      expect(evaluateConditions(ROW, { disable: [4, 5, 6] })).toBe(false);
    });

    it('should handle hide/show single values', () => {
      expect(evaluateConditions(ROW, { hide: 2 })).toBe(false); // ROW.hide doesn't exist
      // Adding hide field for this test
      const rowWithHide = { ...ROW, hide: 1 };

      expect(evaluateConditions(rowWithHide, { hide: 1 })).toBe(true);
    });

    it('should handle nested logical in legacy wrapper', () => {
      const cond = {
        disable: {
          logic: 'all',
          conditions: [
            C('status', 'equal', 1),
            C('level', 'equal', 7),
          ],
        },
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });
  });

  /* ================================================================
     9. MIXED COMPLEX CONDITIONS
     =============================================================== */
  describe('Mixed complex conditions', () => {
    it('should handle mix of primitives, arrays, and complex operators', () => {
      const cond = {
        member_type_name: 'Nominal',                               // simple equality
        status: [1, 2],                                           // array inclusion
        score: { operator: 'greaterThan', value: 80 },           // complex operator
        email: { operator: 'contains', value: '@example.com' },   // string operator
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });

    it('should handle combination with logical groups', () => {
      const cond = {
        status: 1,                                                // simple match
        advanced: {
          logic: 'all',
          conditions: [
            C('age', 'greaterThanOrEqual', 18),
            C('active', 'equal', true),
            {
              logic: 'any',
              conditions: [
                C('level', 'greaterThan', 5),
                C('score', 'greaterThan', 90),
              ],
            },
          ],
        },
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });
  });

  /* ================================================================
     10. EDGE CASES AND ERROR HANDLING
     =============================================================== */
  describe('Edge cases and error handling', () => {
    it('should handle missing fields gracefully', () => {
      expect(evaluateConditions(ROW, { nonexistent_field: 'value' })).toBe(false);
      expect(evaluateConditions(ROW, { missing: { operator: 'equal', value: 123 } })).toBe(false);
    });

    it('should handle empty conditions object', () => {
      expect(evaluateConditions(ROW, {})).toBe(false);
    });

    it('should handle invalid operator gracefully', () => {
      expect(evaluateConditions(ROW, { 
        status: { operator: 'invalidOperator' as ComparisonOperator, value: 1 } 
      })).toBe(false);
    });

    it('should handle root-level array conditions', () => {
      const cond = [
        C('status', 'equal', 999),         // false
        C('level', 'equal', 7),            // true
      ];

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });

    it('should handle primitive root condition', () => {
      // This is an edge case where condition is just a primitive
      expect(evaluateConditions('test', 'test')).toBe(true);
      expect(evaluateConditions('test', 'different')).toBe(false);
    });

    it('should handle conditions with null keys', () => {
      const cond = {
        conditions: [
          C(null, 'equal', ROW),             // comparing entire row
        ],
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });
  });

  /* ================================================================
     11. REAL-WORLD SCENARIOS
     =============================================================== */
  describe('Real-world scenarios', () => {
    it('should handle typical show_on condition', () => {
      const show_on = {
        logic: 'all',
        conditions: [
          C('user_id', 'equal', null),
          C('status', 'equal', 1),
        ],
      };

      expect(evaluateConditions(ROW, show_on)).toBe(true);
    });

    it('should handle typical disable_on condition', () => {
      const disable_on = {
        member_type_name: ['Nominal', 'Associate'],
      };

      expect(evaluateConditions(ROW, disable_on)).toBe(true);
    });

    it('should handle complex hide_on condition', () => {
      const hide_on = {
        logic: 'any',
        conditions: [
          C('active', 'equal', false),
          {
            logic: 'all',
            conditions: [
              C('level', 'lessThan', 5),
              C('score', 'lessThan', 50),
            ],
          },
        ],
      };

      expect(evaluateConditions(ROW, hide_on)).toBe(false);
    });

    it('should handle permission-based conditions', () => {
      const cond = {
        logic: 'all',
        conditions: [
          C('active', 'equal', true),
          C('level', 'greaterThanOrEqual', 5),
          {
            logic: 'any',
            conditions: [
              C('tags', 'contains', 'admin'),
              C('tags', 'contains', 'staff'),
            ],
          },
        ],
      };

      expect(evaluateConditions(ROW, cond)).toBe(true);
    });
  });
});
