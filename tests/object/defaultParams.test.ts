import { mergeDefaultParams } from '../../src/utils/object';


describe('when using string parameters', () => {
  it('should use values from formData when keys are present', () => {
    const params = ['name', 'age'];
    const formData = { name: '<PERSON><PERSON>', age: 30 };
    const expected = { name: 'Ku<PERSON>', age: 30 };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should use the key itself as a fallback value when not in formData', () => {
    const params = ['status'];
    const formData = { name: 'Kumail' };
    const expected = { status: 'status' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should handle a mix of existing and missing keys correctly', () => {
    const params = ['name', 'status', 'id'];
    const formData = { name: 'Kumail', id: 123 };
    const expected = { name: '<PERSON><PERSON>', status: 'status', id: 123 };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });
});


// --- Test Cases for Object-based Mappings ---

describe('when using object-based mappings', () => {
  it('should map a new key to a value from an existing formData key', () => {
    const params = [{ userName: 'name' }];
    const formData = { name: 'Alex' };
    const expected = { userName: 'Alex' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should use the mapping value as a literal when the formData key is missing', () => {
    const params = [{ type: 'maintenance' }];
    const formData = { name: 'Alex' };
    const expected = { type: 'maintenance' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should handle multiple object mappings successfully', () => {
    const params = [{ user: 'name' }, { mode: 'active' }];
    const formData = { name: 'Alex' };
    const expected = { user: 'Alex', mode: 'active' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });
});


// --- Test Cases for Mixed Parameters (Strings and Objects) ---

describe('when using a mix of string and object parameters', () => {
  it('should process a complex mixed array correctly', () => {
    const params = [
      'alpha',                 // Takes value from formData
      { beta: 'gamma' },       // Maps 'gamma' from formData to 'beta'
      { status: 'active' },    // Uses literal 'active' as value for 'status'
      'delta'                  // Uses literal 'delta' as value
    ];

    const formData = { alpha: 42, gamma: 99 };
    const expected = { alpha: 42, beta: 99, status: 'active', delta: 'delta' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });
});


// --- Test Cases for Data Type Variations ---

describe('data type handling', () => {
  it('should correctly handle number values from formData', () => {
    const params = ['id', { value: 'amount' }];
    const formData = { id: 101, amount: 12.50 };
    const expected = { id: 101, value: 12.50 };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should correctly handle boolean values from formData', () => {
    const params = ['isActive', { isEnabled: 'enabled' }];
    const formData = { isActive: true, enabled: false };
    const expected = { isActive: true, isEnabled: false };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should correctly handle null as a valid value from formData', () => {
    const params = ['notes'];
    const formData = { notes: null };
    const expected = { notes: null };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should treat an `undefined` value in formData as missing and apply fallback', () => {
    const params = ['config', { setting: 'optional' }];
    const formData = { config: undefined, optional: undefined };

    // 'config' should fall back to the string 'config'
    // 'setting' should fall back to the string 'optional'
    const expected = { config: 'config', setting: 'optional' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should correctly handle object and array values from formData', () => {
    const params = ['userProfile', { items: 'products' }];

    const formData = {
      userProfile: { id: 1, role: 'admin' },
      products: [{ id: 'a' }, { id: 'b' }]
    };

    const expected = {
      userProfile: { id: 1, role: 'admin' },
      items: [{ id: 'a' }, { id: 'b' }]
    };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });
});


// --- Test Cases for Edge Cases ---

describe('edge cases', () => {
  it('should return an empty object if the params array is empty', () => {
    const params: any[] = [];
    const formData = { user: 'test' };

    expect(mergeDefaultParams(params, formData)).toEqual({});
  });

  it('should return an object with all fallback values if formData is empty', () => {
    const params = ['user', { role: 'guest' }];
    const formData = {};
    const expected = { user: 'user', role: 'guest' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should return an empty object if both params and formData are empty', () => {
    expect(mergeDefaultParams([], {})).toEqual({});
  });

  it('should only use the first key-value pair from a multi-key mapping object', () => {
    // This tests the Object.entries(p)[0] behavior
    const params = [{ key1: 'val1', key2: 'val2' }];
    const formData = { val1: 'correct', val2: 'incorrect' };
    const expected = { key1: 'correct' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });
});


// --- Test Cases for Complex Scenarios & Overwriting ---

describe('complex scenarios and overwriting', () => {
  it('should allow a later rule to overwrite a key defined by an earlier rule', () => {
    // Case 1: Object mapping overwrites a string mapping
    let params: any[] = ['status', { status: 'active' }];
    let formData = { status: 'pending' };
    let expected = { status: 'active' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);

    // Case 2: String mapping overwrites an object mapping
    params = [{ status: 'initial' }, 'status'];
    formData = { status: 'final' };
    expected = { status: 'final' };
    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should handle key name collisions between output keys and formData keys without confusion', () => {
    // The output key 'source' should not be confused with the 'source' key in formData.
    const params = [{ output: 'source' }];
    const formData = { output: 'wrong_value', source: 'correct_value' };
    const expected = { output: 'correct_value' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });

  it('should handle a mapping where the output key and form key are identical', () => {
    const params = [{ user: 'user' }];
    const formData = { user: 'kumail' };
    const expected = { user: 'kumail' };

    expect(mergeDefaultParams(params, formData)).toEqual(expected);
  });
});
